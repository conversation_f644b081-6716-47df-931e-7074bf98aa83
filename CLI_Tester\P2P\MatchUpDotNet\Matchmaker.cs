#nullable enable
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;

#pragma warning disable 0618

namespace NobleConnectLib.MatchUpDotNet
{
    /// <summary>Provides functionality for creating, listing, joining, and leaving matches.</summary>
    /// <remarks>
    /// Opens up a tcp socket connection to the matchmaking server in order to send
    /// commands and receive responses.
    /// Most methods take an optional callback parameter which you can use to get
    /// the response once it is received.
    /// </remarks>
    public class Matchmaker
    {
        public const string TAG = "Match Up: ";

        /// <summary>How often in seconds to send keepalive messages to the matchmaking server</summary>
        /// <remarks>
        /// This helps the matchmaking server know when clients disconnect without getting a chance to
        /// send a disconnect message. This way old matches will be cleaned up quickly and won't linger
        /// in your match lists.
        /// </remarks>
        const int KEEP_ALIVE_TIMEOUT = 5; // Seconds

        /// <summary>The externalIP which is fetched from an external server</summary>
        public static string externalIP = null;

        #region -- Properties ---------------------------------------------------------------------

        /// <summary>The url of the matchmaking server.</summary>
        /// <remarks>
        /// You can use mine for testing but it could go offline at any time so don't even
        /// think of trying to release a game without hosting your own matchmaking server.
        /// </remarks>
        //The url of the matchmaking server"
        //public string? matchmakerURL = "noblewhale.com";
        public string? matchmakerURL = "services.tabulatouch.com";

        /// <summary>The port to connect to on the matchmaking server.</summary>
        /// <remarks>
        /// Leave this as default unless you started the server on a specific port using
        /// <code>
        ///     ./MatchUp -p [PORT NUMBER]
        /// </code>
        /// </remarks>
        // The port to connect to on the matchmaking server
        public int matchmakerPort = 20205;

        /// <summary>How long to wait for a response before giving up</summary>
        //How long to wait for a response before giving up
        public float timeout = 5;

        /// <summary>How many times to attempt to re-connect to the matchmaking server if connection is lost.</summary>
        /// <remarks>
        /// When the connection is lost the Matchmaker will automatically try and reconnect this many times.
        /// If you wish to implement your own reconnect behaviour, use the onLostConnectonToMatchmakingServer action
        /// and set this to 0 to disable the default behaviour.
        /// </remarks>
        //How many times to attempt to re-connect to the matchmaking server if connection is lost
        public int maxReconnectionAttempts = 5;

        /// <summary>A web service to query to retrieve the external IP of this computer.</summary>
        //A web service to query to retrieve the external IP of this computer")]
        public string externalIPSource = "http://ipv4.noblewhale.com";
        
        /// <summary>You can use this action to be informed if connection is lost to the matchmaking server</summary>
        /// <remarks>
        /// Generally this means someone pulled out the internet plug or something equally catastrophic.
        /// </remarks>
        public Action<Exception> onLostConnectionToMatchmakingServer = null;

        public bool autoConnect = true;

        #endregion

        #region -- Runtime data -------------------------------------------------------------------

        IPAddress matchmakerIP;

        /// <summary>The connection to the matchmaking server.</summary>
        TcpClient matchmakingClient;
        /// <summary>Used for sending to the matchmaking server.</summary>
        NetworkStream networkStream;

        /// <summary>Used for receiving from the matchmaking server.</summary>
        StreamReader streamReader;
        
        /// <summary>Keep track of open Transactions so we can call the appropriate onResponse method when a response is received.</summary>
        /// <remarks>
        /// Each request that is sent generates a Transaction with a unique transaction ID.
        /// When the matchmaking server response to a request it will include the ID in the response.
        /// When the response is received the transaction ID is used to look up the transaction
        /// so that it can be completed and the onResponse handler can be called.
        /// </remarks>
        Dictionary<string, Transaction> transactions = new Dictionary<string, Transaction>();

        /// <summary>The current Match. This is set whenever a Match is created or joined.</summary>
        public Match currentMatch;

        
        
        bool isConnecting;
        
        string receivedMessage = "";
        char[] buffer = new char[1024];

        private CancellationTokenSource tokenSource;
        private CancellationToken token;
        
        private DateTime lastKeepAliveDateTime = DateTime.MinValue;
        
        private System.Timers.Timer updateTimer;
        #endregion

        #region -- exUnity stuff --------------------------------------------------------------------

        public Matchmaker()
        {
            tokenSource = new CancellationTokenSource();
            token = tokenSource.Token;

            Start();
        }
        
        // Start() is now called from the ctor
        /// <summary>Set up the networking stuff</summary>
        async void Start()
        {
            // We need to get the external IP
            if (autoConnect)
            {
               await ConnectToMatchmaker(token);
            }
        }
        

        /// <summary>Check for incoming responses from the matchmaking server.</summary>
        async void Update(Object source, ElapsedEventArgs e)
        {

            receivedMessage = await ReadData(token);
            
            if (matchmakingClient != null && matchmakingClient.Connected)
            {
                var diff = DateTime.Now.Subtract(lastKeepAliveDateTime);
                if (diff.TotalSeconds > KEEP_ALIVE_TIMEOUT)
                {
                    await KeepAlive();
                    lastKeepAliveDateTime = DateTime.Now;
                }
            }
        }

        #endregion

        #region -- Public interface ---------------------------------------------------------------
        
        /// <summary>
        /// Connect to the matchmaking server. This called automatically in Start().
        /// </summary>
        /// <remarks>
        /// Called automatically on start and also when reconnecting after a lost connection.
        /// You can also call it yourself if you implement your own re-connection scheme or
        /// for whatever other reason you may find.
        /// </remarks>
        /// <returns></returns>
        public async Task ConnectToMatchmaker(CancellationToken _token)
        {
            while (isConnecting)
            {
                await Task.Delay(1000);
            }
            
            isConnecting = true;

            if (string.IsNullOrEmpty(externalIP))
            {
                externalIP = FetchExternalIP(externalIPSource);
            }

            foreach (var transaction in transactions)
            {
                transaction.Value.Failed("Lost connection to MatchUp server.");
                tokenSource.Cancel();
            }

            int maxTries = maxReconnectionAttempts;
            int attempts = 0;
            while (attempts < maxTries && (matchmakingClient == null || !matchmakingClient.Connected)
                   && !_token.IsCancellationRequested)
            {
                matchmakerIP = await ResolveMatchmakerURLAsync();

                if (matchmakerIP == null)
                {
                    await Task.Delay(5000);
                    attempts++;
                    Console.WriteLine("Attempting to resolve matchmakerURL...again. Attemps remaining: "
                                      + (maxTries - attempts).ToString());
                    continue;
                }
                
                if (matchmakingClient != null) matchmakingClient.Close();
                matchmakingClient = new TcpClient(AddressFamily.InterNetwork);
                try
                {
                    Console.WriteLine("[Matchmaker] Connecting to the matchmaking server...");
                    await matchmakingClient.ConnectAsync(matchmakerIP, matchmakerPort);
                    Console.WriteLine("[Matchmaker] Connected to the matchmaking server");
                }
                catch (SocketException e)
                {
                    Console.WriteLine(TAG + "Failed attempting to connect to the matchmaking server. Is it running?\n" + e);
                }
                catch (Exception e)
                {
                    Console.WriteLine(TAG + "Failed to connect to the matchmaking server.\n" + e);
                }

                bool isConnected;
                isConnected = matchmakingClient.Connected;
 
                if (!isConnected)
                {
                    await Task.Delay(5000);
                }

                attempts++;
            }


            if (matchmakingClient != null && matchmakingClient.Connected)
            {
                if (networkStream != null) networkStream.Dispose();
                if (streamReader != null) streamReader.Dispose();
                networkStream = matchmakingClient.GetStream();
                streamReader = new StreamReader(networkStream);
                
                updateTimer = new System.Timers.Timer();
                updateTimer.Interval = 100;
                updateTimer.Elapsed += Update;
                updateTimer.AutoReset = true;

                if (!updateTimer.Enabled)
                {
                    updateTimer.Start();
                }
            }
            else
            {
                Console.WriteLine(TAG + "Final failed to connect to the matchmaking server. Is it running?");
            }
            
            isConnecting = false;
        }

        public void Disconnect()
        {
            updateTimer.Stop();
            tokenSource.Cancel();
            
            matchmakingClient.Close();
            isConnecting = false;
            networkStream.Dispose();
            streamReader.Dispose();
            matchmakingClient = null;
            networkStream = null;
            streamReader = null;
        }

        /// <summary>Send the command to the matchmaking server to create a new match.</summary>
        /// <param name="maxClients">The maximum number of clients to allow. Once a match is full it is no longer returned in match listings (until a client leaves).</param>
        /// <param name="matchData">Optional match data to include with the match. This is a good place to store your connection data.</param>
        /// <param name="matchName">The name of the match.</param>
        /// <param name="onCreateMatch">Optional callback method to call when a response is received from the matchmaking server.</param>
        public void CreateMatch(int maxClients, Dictionary<string, MatchData> matchData = null, Action<bool, Match> onCreateMatch = null)
        {
            if (matchData == null) matchData = new Dictionary<string, MatchData>();

            if (!matchData.ContainsKey("internalIP")) matchData["internalIP"] = GetLocalAddress(AddressFamily.InterNetwork);
            if (!matchData.ContainsKey("externalIP")) matchData["externalIP"] = externalIP;

            if (matchmakerURL == "grabblesgame.com" || matchmakerURL == "noblewhale.com")
            {
                // If you're using my matchmaking server then we need to include some sort of ID to keep your game's matches separate from everyone else's
                // UNITY version: Application.productName
                if (!matchData.ContainsKey("applicationID"))
                {
                    // .NET version:
                    var productName = Assembly.GetEntryAssembly()?.GetName().Name;
                    if (productName != null)
                        matchData["applicationID"] = productName;
                }
            }
            currentMatch = null;
            SendCommand(
                Command.CREATE_MATCH,
                Match.Serialize(maxClients, matchData),
                (success, transaction) => OnCreateMatchInternal(success, transaction.response, matchData, onCreateMatch)
            );
        }

        /// <summary>Set a single MatchData value and immediately send it to the matchmaking server.</summary>
        /// <remarks>
        /// Ex:
        /// <code>matchUp.SetMatchData("eloScore", 100);</code>
        /// </remarks>
        /// <param name="key">The key</param>
        /// <param name="matchData">The value</param>
        /// <param name="onSetMatchData">Optional callback method to call when a response is received from the matchmaking server.</param>
        public void SetMatchData(string key, MatchData matchData, Action<bool, Match> onSetMatchData = null)
        {
            if (currentMatch == null || currentMatch.id == -1)
            {
                Console.WriteLine("Can not SetMatchData until after a match has been created: " + key);
                onSetMatchData(false, null);
                return;
            }
            currentMatch.matchData[key] = matchData;
            SendCommand(
                Command.SET_MATCH_DATA,
                currentMatch.id + "|" + matchData.Serialize(key),
                (success, response) => {
                    if (onSetMatchData != null) onSetMatchData(success, currentMatch);
                }
            );
        }

        /// <summary>Replace all existing match data with new match data.</summary>
        /// <remarks>
        /// Ex:
        /// <code>
        /// var newMatchData = new Dictionary<string, MatchData>() {
        ///    { "Key1", "value1" },
        ///    { "Key2", 3.14159 }
        /// };
        /// matchUp.SetMatchData(newMatchData);
        /// </code>
        /// </remarks>
        /// <param name="matchData">A Dictionary of new MatchData</param>
        /// <param name="onSetMatchData">Optional callback method to call when a response is received from the matchmaking server.</param>
        public void SetMatchData(Dictionary<string, MatchData> matchData, Action<bool, Match> onSetMatchData = null)
        {
            if (currentMatch == null || currentMatch.id == -1)
            {
                Console.WriteLine("Can not SetMatchData until after a match has been created");
                onSetMatchData(false, null);
                return;
            }

            currentMatch.matchData = matchData;
            UpdateMatchData(onSetMatchData);
        }

        /// <summary>Merge new MatchData with existing MatchData and immediately send it all to the matchmaking server.</summary>
        /// <remarks>
        /// Ex:
        /// <code>
        /// var additionalMatchData = new Dictionary<string, MatchData>() {
        ///    { "Key1", new MatchData("value1") },
        ///    { "Key2", new MatchData(3.14159) }
        /// };
        /// matchUp.UpdateMatchData(additionalMatchData);
        /// </code>
        /// </remarks>
        /// <param name="additionalData">A Dictionary of additional MatchData to merge into existing match data</param>
        /// <param name="onUpdateMatchData">Optional callback method to call when a response is received from the matchmaking server.</param>
        public void UpdateMatchData(Dictionary<string, MatchData> additionalData, Action<bool, Match> onUpdateMatchData = null)
        {
            if (currentMatch == null || currentMatch.id == -1)
            {
                Console.WriteLine("Can not UpdateMatchData until after a match has been created");
                onUpdateMatchData(false, null);
                return;
            }

            // Add new MatchData entries and replace existing one
            foreach (KeyValuePair<string, MatchData> kv in additionalData)
            {
                currentMatch.matchData[kv.Key] = kv.Value;
            }
            UpdateMatchData(onUpdateMatchData);
        }

        /// <summary>Send current MatchData to the matchmaking server.</summary>
        /// <remarks>
        /// Ex:
        /// <code>
        /// matchUp.currentMatch.matchData["Key1"] = 3.14159;
        /// matchUp.currentMatch.matchData["Key2"] = "Hello world";
        /// matchUp.UpdateMatchData();
        /// </code>
        /// </remarks>
        /// <param name="onUpdateMatchData">Optional callback method to call when a response is received from the matchmaking server.</param>
        public void UpdateMatchData(Action<bool, Match> onUpdateMatchData = null)
        {
            if (currentMatch == null || currentMatch.id == -1)
            {
                Console.WriteLine("Can not UpdateMatchData until after a match has been created");
                onUpdateMatchData(false, null);
                return;
            }

            SendCommand(
                Command.SET_MATCH_DATA,
                currentMatch.id + "|" + MatchData.SerializeDictionary(currentMatch.matchData),
                (success, response) => {
                    if (onUpdateMatchData != null) onUpdateMatchData(success, currentMatch);
                }
            );
        }

        /// <summary>Destroy a match. This also removes all Client entries and MatchData on the matchmaking server.</summary>
        /// <param name="onDestroyMatch">Optional callback method to call when a response is received from the matchmaking server.</param>
        public void DestroyMatch(Action<bool> onDestroyMatch = null)
        {
            if (currentMatch == null || currentMatch.id == -1)
            {
                // There is no match to destroy
                Console.WriteLine("Can not DestroyMatch because there is no current match.");
                if (onDestroyMatch != null) onDestroyMatch(false);
                return;
            }
            SendCommand(
                Command.DESTROY_MATCH,
                currentMatch.id.ToString(),
                (success, response) => {
                    if (onDestroyMatch != null) onDestroyMatch(success);
                }
            );
        }

        /// <summary>Join one of the matches returned my GetMatchList().</summary>
        /// <remarks>
        /// You can use the callback to get the Match object after it is received from the matchmaking server.
        /// Once the match is joined you'll have access to all the match's MatchData.
        /// </remarks>
        /// <param name="match">The Match to join. Generally this will come from GetMatchList()</param>
        /// <param name="onJoinMatch">Optional callback method to call when a response is received from the matchmaking server.</param>
        public void JoinMatch(Match match, Action<bool, Match> onJoinMatch = null)
        {
            if (match == null || match.id == -1)
            {
                // There is no match to join
                Console.WriteLine("Can not JoinMatch because the match is invalid (null or id == -1)");
                onJoinMatch(false, match);
                return;
            }
            currentMatch = match;
            SendCommand(
                Command.JOIN_MATCH,
                currentMatch.id.ToString(),
                (success, transaction) => OnJoinMatchInternal(success, transaction.response, match, onJoinMatch)
            );
        }

        /// <summary>Leave a match.</summary>
        /// <param name="onLeaveMatch">Optional callback method to call when a response is received from the matchmaking server.</param>
        public void LeaveMatch(Action<bool> onLeaveMatch = null)
        {
            if (currentMatch == null || currentMatch.id == -1)
            {
                // There is no match to leave
                Console.WriteLine("Can not LeaveMatch because there is no current match.");
                onLeaveMatch(false);
                return;
            }
            SendCommand(
                Command.LEAVE_MATCH,
                currentMatch.clientID.ToString(),
                (success, response) => {
                    if (onLeaveMatch != null) onLeaveMatch(success);
                }
            );
        }

        /// <summary>Get info on a single match</summary>
        /// <param name="onGetMatch">Callback method to call when the response is received from the matchmaking server</param>
        /// <param name="id">The ID of the match to fetch info for</param>
        /// <param name="includeMatchData">Whether or not to include match data in the response</param>
        public void GetMatch(Action<bool, Match> onGetMatch, long id, bool includeMatchData = true)
        {
            char includeMatchDataChar = includeMatchData ? '1' : '0';
            SendCommand(
                Command.GET_MATCH,
                id + "," + includeMatchDataChar,
                (success, transaction) => OnGetMatchInternal(success, transaction.response, onGetMatch)
            );
        }

        /// <summary>Get the match list, optionally filtering the results.</summary>
        /// <remarks>
        /// Ex:
        /// <code>
        /// var filters = new List<MatchFilter>(){
        ///     new MatchFilter("eloScore", 100, MatchFilter.OperationType.GREATER),
        ///     new MatchFilter("eloScore", 300, MatchFilter.OperationType.LESS)
        /// };
        /// matchUp.GetMatchList(OnMatchList, filters);
        /// ...
        /// void OnMatchList(bool success, Match[] matches)
        /// {
        ///     matchUp.JoinMatch(matches[0], OnJoinMatch);
        /// }
        /// </code>
        /// </remarks>
        /// <param name="onMatchList">Callback method to call when a response is received from the matchmaking server.</param>
        /// <param name="pageNumber">Used with resultsPerPage. Determines which page of results to return. Defaults to 0.</param>
        /// <param name="resultsPerPage">User with pageNumber. Determines how many matches to return for each page. Defaults to 10.</param>
        /// <param name="filters">Optional List of Filters to use when fetching the match list</param>
        /// <param name="includeMatchData">
        /// By default match data is included for every match in the list. 
        /// If you don't need / want this you can pass false in here and save some bandwidth. 
        /// If you don't retrieve match data here you can still get it when joining the match.
        /// </param>
        public void GetMatchList(Action<bool, Match[]> onMatchList, int pageNumber = 0, int resultsPerPage = 10, List<MatchFilter> filters = null, bool includeMatchData = true)
        {
            if (matchmakerURL == "grabblesgame.com" || matchmakerURL == "noblewhale.com")
            {
                if (filters == null)
                {
                    filters = new List<MatchFilter>();
                }
                if (filters.Find(x => x.key == "applicationID") == null)
                {
                    // If you're using my matchmaking server then we need to include some sort of ID to keep your game's matches separate from everyone else's
                    var productName = Assembly.GetEntryAssembly()?.GetName().Name;
                    if (productName != null)
                        filters.Add(new MatchFilter("applicationID", productName));
                }
            }
            string filterString = "";
            if (filters != null) filterString = "|" + MatchFilter.Serialize(filters);
            char includeMatchDataChar = includeMatchData ? '1' : '0';
            SendCommand(
                Command.GET_MATCH_LIST,
                pageNumber + "," + resultsPerPage + "," + includeMatchDataChar + filterString,
                (success, transaction) => OnGetMatchListInternal(success, transaction.response, onMatchList)
            );
        }
        
        public string FetchExternalIP(string ipSource)
        {
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(ipSource);
            request.Timeout = 5000;
            request.Method = "GET";
            
            try
            {
                using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                {
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        using (var reader = new System.IO.StreamReader(
                                   response.GetResponseStream()!, Encoding.ASCII))
                        {
                            string responseText = reader.ReadToEnd();
                            return IPAddress.Parse(responseText).ToString();
                        }
                        
                        
                    }
                    else
                    {
                        Console.WriteLine($"Failed fetching IP. Response: {response.ToString()}");
                    }
                }
            }
            catch (WebException webException)
            {
                Console.WriteLine($"Failed fetching IP with exception: {webException.Message}");
            }

            return string.Empty;
        }


        /// <summary>Fetch the external IP (async)</summary>
        /// <param name="ipSource">The url from which to fetch the IP</param>
        public async Task<string> FetchExternalIPAsync(string ipSource, CancellationToken _token)
        {
            if (!_token.IsCancellationRequested)
            {
                using (var httpClient = new HttpClient())
                {
                    var request = new HttpRequestMessage
                    {
                        Method = HttpMethod.Get,
                        RequestUri = new Uri(ipSource)
                    };

                    using (var response = await httpClient.SendAsync(request))
                    {
                        string responseString = await response.Content.ReadAsStringAsync();
                        string ipString = string.Empty;

                        try
                        {
                            ipString = IPAddress.Parse(responseString).ToString();
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine("Failed fetching IP. Response: " + responseString);
                            Console.WriteLine(e);
                        }

                        return ipString;
                    }
                }
            }
            return String.Empty;
        }

        /// <summary>Select between internal and external IP.</summary>
        /// <remarks>
        /// Most of the time we connect to the externalIP but when connecting to another PC on the same local network or 
        /// another build on the same computer we need to use the local address or localhost instead
        /// </remarks>
        /// <param name="hostExternalIP">The host's external IP</param>
        /// <param name="hostInternalIP">The host's internal IP</param>
        /// <returns></returns>
        public static string PickCorrectAddressToConnectTo(string hostExternalIP, string hostInternalIP)
        {
            if (hostExternalIP == externalIP && !string.IsNullOrEmpty(hostInternalIP))
            {
                // Client and host are behind the same router
                if (hostInternalIP == GetLocalAddress(AddressFamily.InterNetwork))
                {
                    // Host is running on the same computer as client, two separate builds
                    return "127.0.0.1";
                }
                else
                {
                    // Host is on the same local network as client
                    return hostInternalIP;
                }
            }
            else
            {
                // Host is somewhere out on the internet
                return hostExternalIP;
            }
        }

        /// <summary>
        /// Gets the a local address by looping through all network interfaces and returning first address from the first interface whose OperationalStatus is Up and whose
        /// address family matches the provided family.
        /// </summary>
        /// <returns>The local address as a string or an empty string if there is none</returns>
        public static string GetLocalAddress(AddressFamily family)
        {
            try
            {
                foreach (NetworkInterface item in NetworkInterface.GetAllNetworkInterfaces())
                {
                    if (item.OperationalStatus == OperationalStatus.Up)
                    {
                        foreach (UnicastIPAddressInformation ip in item.GetIPProperties().UnicastAddresses)
                        {
                            if (ip.Address.AddressFamily == family)
                            {
                                return ip.Address.ToString().Trim();
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                return "";
            }

            return "";
        }

        #endregion

        #region -- Internal handlers --------------------------------------------------------------

        /// <summary>Parses the CreateMatch response to get the match id and clientID</summary>
        void OnCreateMatchInternal(bool success, string response, Dictionary<string, MatchData> matchData, Action<bool, Match> onCreateMatch)
        {
            if (!success)
            {
                currentMatch = null;
                if (onCreateMatch != null) onCreateMatch(success, currentMatch);
                return;
            }

            string[] parts = response.Split(',');
            try
            {
                long id = long.Parse(parts[0]);
                currentMatch = new Match(id, matchData);
                currentMatch.clientID = long.Parse(parts[1]);
            }
            catch (Exception e)
            {
                Console.WriteLine(TAG + "Error parsing CreateMatch response from matchmaking server." + e);
                success = false;
            }

            if (onCreateMatch != null) onCreateMatch(success, currentMatch);
        }

        /// <summary>Parses the clientID and MatchData returned by the matchmaking server.</summary>
        void OnJoinMatchInternal(bool success, string response, Match match, Action<bool, Match> onJoinMatch)
        {
            int endPos = 0;
            int startPos = 0;
            try
            {
                // Client id
                endPos = response.IndexOf(',', startPos);
                if (endPos == -1)
                {
                    currentMatch.clientID = int.Parse(response);
                }
                else
                {
                    currentMatch.clientID = int.Parse(response.Substring(startPos, (endPos - startPos)));
                    startPos = endPos + 1;

                    // The rest of the match data
                    match.matchData = MatchData.DeserializeDictionary(response, startPos, ref endPos);
                    startPos = endPos + 1;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(TAG + "Error parsing JoinMatch response from matchmaking server." + e);
                success = false;
            }

            if (onJoinMatch != null)
            {
                onJoinMatch(success, match);
            }
        }

        /// <summary>Parses the match list returned by the matchmaking server</summary>
        void OnGetMatchInternal(bool success, string response, Action<bool, Match> onMatch)
        {
            int endPos = 0;
            int startPos = 0;

            if (response == "")
            {
                onMatch(false, null);
                return;
            }

            Match match = null;
            try
            {
                // Match id
                endPos = response.IndexOf(',', startPos);
                int matchID = int.Parse(response.Substring(startPos, (endPos - startPos)));
                startPos = endPos + 1;

                // Create the match object
                match = new Match(matchID);

                // Add the match data to the match
                match.matchData = MatchData.DeserializeDictionary(response, startPos, ref endPos);
                startPos = endPos + 1;
            }
            catch (Exception e)
            {
                Console.WriteLine(TAG + "Error parsing GetMatchList response from matchmaking server." + e);
                success = false;
            }

            if (onMatch != null)
            {
                onMatch(success, match);
            }
        }

        /// <summary>Parses the match list returned by the matchmaking server</summary>
        void OnGetMatchListInternal(bool success, string response, Action<bool, Match[]> onMatchList)
        {
            if (success == false)
            {
                if (onMatchList != null) onMatchList(success, null);
                return;
            }

            int endPos = 0;
            int startPos = 0;

            List<Match> matches = new List<Match>();
            try
            {
                while (endPos != -1 && endPos < response.Length && startPos < response.Length)
                {
                    // Match id
                    endPos = response.IndexOf(',', startPos);
                    int matchID = int.Parse(response.Substring(startPos, (endPos - startPos)));
                    startPos = endPos + 1;

                    // Match name
                    // This is actually deprecated and unused but left in so I don't have to change the server
                    Message.ParseQuoted(response, startPos, ref endPos);
                    bool hasMatchData = endPos < response.Length && response[endPos] == ',';
                    startPos = endPos + 1;

                    // Create the match object
                    Match match = new Match(matchID);

                    // Check if there is match data that needs parsing
                    if (hasMatchData)
                    {
                        // Add the match data to the match
                        match.matchData = MatchData.DeserializeDictionary(response, startPos, ref endPos);
                        startPos = endPos + 1;
                    }

                    // Add the match to the list
                    matches.Add(match);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(TAG + "Error parsing GetMatchList response from matchmaking server." + e);
                success = false;
            }

            if (onMatchList != null)
            {
                onMatchList(success, matches.ToArray());
            }
        }

        #endregion

        #region -- Internal -----------------------------------------------------------------------

		/// <summary>Send a command to the matchmaking server.</summary>
		async void SendCommand(Command command, string textToSend = "", Action<bool, Transaction> onResponse = null)
        {
            var request = new Message(command, textToSend);
            await SendCommandAsync(request, onResponse);
        }

        /// <summary>Send a Command to the matchmaking server and wait for a response.</summary>
        /// <remarks>
        /// Creates a transaction for the request if a response is expected.
        /// Also starts a coroutine to timeout the transaction if no response is received.
        /// </remarks>
        private async Task SendCommandAsync(Message request, Action<bool, Transaction> onResponse = null)
        {
            while (matchmakingClient == null)
            {
                Console.WriteLine("[Matchmaker] waiting for matchmakingClient to be istantiated");
                await Task.Delay(1000).ConfigureAwait(false);
            }
            
            while (!matchmakingClient.Connected)
            {
                Console.WriteLine("[Matchmaker] waiting for matchmakingClient to be connected");
                await Task.Delay(1000).ConfigureAwait(false);
            }

            string transactionID = "";
            Transaction transaction = null;
            if (onResponse != null)
            {
                transactionID = Transaction.GenerateID();

                transaction = new Transaction(transactionID, request, onResponse);
                transactions[transactionID] = transaction;
            }

            // Send the command
            string textToSend = (int)request.command + "|" + transactionID + "|" + request.payload + "\n";
            Console.WriteLine("[Matchmaker] sending command: " + textToSend);
            byte[] bytesToSend = Encoding.UTF8.GetBytes(textToSend);
            try
            {
                await networkStream.WriteAsync(bytesToSend, 0, bytesToSend.Length);
            }
            catch (Exception e)
            {
                transaction.Failed("Lost connection to MatchUp server.");
                onLostConnectionToMatchmakingServer(e);
            }

            if (onResponse != null)
            {
                //Coroutine timeoutProcess = StartCoroutine(TimeoutTransaction(transaction));
                _ = Task.Run(() => TimeoutTransaction(transaction, token));
                
                // Use CancellationToken inside TimeoutTransaction
                // and assign the token to timeoutProcess
                if (transaction != null) transaction.token = token;
            }
            
            
        }

        /// <summary>Wait to see if a transaction times out.</summary>
        /// <remarks>
        /// If no response is received then the transaction has failed.
        /// The transaction's onResponse method will be called with success = false.
        /// </remarks>
        /// <param name="transaction">The transaction that is timing out</param>
        private async Task TimeoutTransaction(Transaction transaction, CancellationToken _token)
        {
            await Task.Delay(1000 * (int)timeout);
            // One last chance to complete the transaction
            // This helps prevent false negatives caused by long scene loads
            // Without the extra read the transaction will appear to have timed out
            // when the scene finishes loading because the check below runs before
            // the Update() loop that would normally read the message.
            
            await ReadData(token);
            
            if (!transaction.isComplete)
            {
                transactions.Remove(transaction.transactionID);
                transaction.Timeout();
            }
        }

        /// <summary>Resolve the matchmaker url to an ip</summary>
        private async Task<IPAddress?> ResolveMatchmakerURLAsync()
        {
            var asyncResult = Dns.BeginGetHostAddresses(matchmakerURL, null, null);

            while (!asyncResult.IsCompleted)
            {
                await Task.Delay(500).ConfigureAwait(false);
            }

            IPAddress[] addresses = null;
            try
            {
                addresses = Dns.EndGetHostAddresses(asyncResult);
            }
            catch (Exception)
            {
                // Do nothing, probably no internet
            }

            if (addresses == null || addresses.Length == 0)
            {
                Console.WriteLine(TAG + "Failed to resolve matchmakerUrl: " + matchmakerURL);
            }

            if (addresses != null)
                foreach (var address in addresses)
                {
                    if (address.AddressFamily == AddressFamily.InterNetwork)
                    {
                        return address;
                    }
                }

            return null;
        }

        /// <summary>Send the keep-alive message to the matchmaking server so it knows we are still here.</summary>
        private async Task KeepAlive()
        {
            try
            {
                if (matchmakingClient != null && networkStream != null && streamReader != null && networkStream.CanWrite)
                {
                    await networkStream.WriteAsync(new byte[] { (byte)'\n' }, 0, 1);
                    Console.WriteLine("[Matchmaker] Keep Alive sent");
                }
            }
            catch (Exception e) 
            {
                if (matchmakingClient != null) matchmakingClient.Close();
                if (onLostConnectionToMatchmakingServer != null)
                {
                    onLostConnectionToMatchmakingServer(e);
                }
            }
        }

        /// <summary>Read incoming data from the matchmaking server</summary>
        private async Task<string> ReadData(CancellationToken _token) 
        {

            // Check if there is anything to read and if there is read it
            while (networkStream.CanRead && networkStream.DataAvailable && !_token.IsCancellationRequested)
            {
                // Read up to buffer length
                int numCharsRead = 0;
                numCharsRead = await streamReader.ReadAsync(buffer, 0, buffer.Length).ConfigureAwait(false);

                // Convert to string for easy splitting
                string incoming = new string(buffer, 0, numCharsRead);
                // Split on delimeter
                string[] messages = incoming.Split('\n');

                // There may be 0, 1, or many messages.
                // If the delimeter does not appear, then the buffer contains a single
                // partial message that is stored for when the rest of it arrives
                // If the delimeter is present then messages[] will contain all
                // of the complete messages, except for the final entry which will
                // contain the next partial message.
                for (int i = 0; i < messages.Length - 1; i++)
                {
                    string message = receivedMessage + messages[i];
                    receivedMessage = ""; // Only prepended to the first message, so unset after using

                    // Parse the transaction ID and response body
                    int pipePos = message.IndexOf('|');
                    string transactionID = message.Substring(0, pipePos);
                    string response = message.Substring(pipePos + 1);

                    // Find the corresponding transaction that this is a response for
                    Transaction t;
                    bool success = transactions.TryGetValue(transactionID, out t);
                    if (success)
                    {
                        // Complete the transaction and remove it from the list
                        transactions.Remove(transactionID);
                        if (t != null) t.Complete(response);
                    }
                    else
                    {
                        Console.WriteLine(TAG + "Received a response for which there is no open transaction: " + message);
                    }
                }
                // Store the final entry as the new partial message to be used when the next bit of data arrives
                receivedMessage += messages[messages.Length - 1];
            }

            return receivedMessage;
        }
        
        #endregion
    }
}
