using System;

namespace Tabula.PWG.MobileController
{
    /// <summary>
    /// .NET equivalent of Unity's Debug class for console applications
    /// Provides logging functionality with timestamps and consistent formatting
    /// </summary>
    public static class NetDebug
    {
        /// <summary>
        /// Equivalent to Unity's Debug.Log() - writes info message to console
        /// </summary>
        /// <param name="message">Message to log</param>
        public static void Log(object message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            Console.WriteLine($"[{timestamp}] [INFO] {message}");
        }

        /// <summary>
        /// Equivalent to Unity's Debug.LogWarning() - writes warning message to console
        /// </summary>
        /// <param name="message">Warning message to log</param>
        public static void LogWarning(object message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            Console.WriteLine($"[{timestamp}] [WARN] {message}");
        }

        /// <summary>
        /// Equivalent to Unity's Debug.LogError() - writes error message to console
        /// </summary>
        /// <param name="message">Error message to log</param>
        public static void LogError(object message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            Console.WriteLine($"[{timestamp}] [ERROR] {message}");
        }

        /// <summary>
        /// Equivalent to Unity's Debug.LogException() - writes exception to console
        /// </summary>
        /// <param name="exception">Exception to log</param>
        public static void LogException(Exception exception)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            Console.WriteLine($"[{timestamp}] [EXCEPTION] {exception.Message}");
            Console.WriteLine($"[{timestamp}] [EXCEPTION] {exception.StackTrace}");
        }

        /// <summary>
        /// Logs a message with custom level
        /// </summary>
        /// <param name="level">Log level (INFO, WARN, ERROR, etc.)</param>
        /// <param name="message">Message to log</param>
        public static void LogWithLevel(string level, object message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            Console.WriteLine($"[{timestamp}] [{level}] {message}");
        }
    }
}
