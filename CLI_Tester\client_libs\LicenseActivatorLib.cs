﻿using System;
using System.Diagnostics;
using System.IO;
using Tabula.WebServices;
using Tabula.WebServices.Arguments;
using UnityEngine;

// PMController version, stripped down

namespace Tabula.Licensing.LicenseActivator
{
	public static class LicenseActivatorLib
	{
		public static Action<string> OnLog;
		public static Action<Exception> OnException;

		public static void log(string message)
		{
			UnityEngine.Debug.Log(message);
		}

		public static void log_error(string message)
		{
			UnityEngine.Debug.LogError(message);
		}

		public static void log_exception(Exception ex)
		{
			UnityEngine.Debug.LogException(ex);
		}


		#region Calls to WebServices

		public static int CheckAuthCode(string auth_code)
		{
			if (string.IsNullOrEmpty(auth_code))
			{
				log_error("Empty authcode");
				return -1;
			}

			LicenseAuthCodeCheck_Response res;

			try
			{
				res = Client.LicenseAuthCodeCheck("sargame", auth_code: auth_code);

				if (res == null)
				{
					log_error("Null response");
					return -2;
				}
			}
			catch (Exception ex)
			{
				log_exception(ex);
				return -3;
			}

			// Can be either (1 or 2) normal or admin auth_code
			return res.result;
		}


		public static int CalibrationImageUpload(string auth_code, string image_path)
		{
			if (string.IsNullOrEmpty(auth_code))
			{
				throw new Exception("AuthCode is needed");
			}

			if (!File.Exists(image_path))
			{
				throw new Exception("Image path not found");
			}

			var resp = Client.CalibrationImageUpload("sargame", auth_code, image_path);

			if (resp != null)
				return resp.result;
			else
				return -1;
		}

		#endregion
	}
}

// Try to avoid AspNet.Core.Mvc dependency

namespace Microsoft.AspNetCore.Mvc
{
	public class ObjectResult  //: ActionResult, IStatusCodeActionResult, IActionResult
	{
		/*
        private MediaTypeCollection _contentTypes;
        */
		//[ActionResultObjectValue]
		public object Value { get; set; }

		/*
		public FormatterCollection<IOutputFormatter> Formatters { get; set; }

		public MediaTypeCollection ContentTypes
		{
			get
			{
				return _contentTypes;
			}
			set
			{
				_contentTypes = value ?? throw new ArgumentNullException("value");
			}
		}

		public Type DeclaredType { get; set; }
        */

		//
		// Summary:
		//     Gets or sets the HTTP status code.
		public int? StatusCode { get; set; }

		public ObjectResult(object value)
		{
			Value = value;
			/*
			Formatters = new FormatterCollection<IOutputFormatter>();
			_contentTypes = new MediaTypeCollection();
            */
		}

		/*
		public override Task ExecuteResultAsync(ActionContext context)
		{
			return context.HttpContext.RequestServices.GetRequiredService<IActionResultExecutor<ObjectResult>>().ExecuteAsync(context, this);
		}
        */

		/*
		//
		// Summary:
		//     This method is called before the formatter writes to the output stream.
		public virtual void OnFormatting(ActionContext context)
		{
			if (context == null)
			{
				throw new ArgumentNullException("context");
			}

			if (StatusCode.HasValue)
			{
				context.HttpContext.Response.StatusCode = StatusCode.Value;
				ProblemDetails problemDetails;
				if ((problemDetails = Value as ProblemDetails) != null && !problemDetails.Status.HasValue)
				{
					problemDetails.Status = StatusCode.Value;
				}
			}
		}
        */
	}

	//
	// Summary:
	//     An Microsoft.AspNetCore.Mvc.ObjectResult that when executed performs content
	//     negotiation, formats the entity body, and will produce a Microsoft.AspNetCore.Http.StatusCodes.Status200OK
	//     response if negotiation and formatting succeed.
	//[DefaultStatusCode(200)]
	public class OkObjectResult : ObjectResult
	{
		private const int DefaultStatusCode = 200;

		//
		// Summary:
		//     Initializes a new instance of the Microsoft.AspNetCore.Mvc.OkObjectResult class.
		//
		// Parameters:
		//   value:
		//     The content to format into the entity body.
		public OkObjectResult(object value)
			: base(value)
		{
			base.StatusCode = 200;
		}
	}
}
