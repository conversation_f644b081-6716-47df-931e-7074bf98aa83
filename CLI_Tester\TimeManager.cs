using System;
using System.Diagnostics;

namespace Tabula.PWG.MobileController
{
    /// <summary>
    /// .NET equivalent of Unity's Time class for console applications
    /// Provides time management functionality without Unity dependencies
    /// </summary>
    public static class TimeManager
    {
        private static readonly Stopwatch _stopwatch = Stopwatch.StartNew();
        private static readonly DateTime _startTime = DateTime.Now;

        /// <summary>
        /// Equivalent to Unity's Time.time - returns elapsed time in seconds since application start
        /// </summary>
        public static float time => (float)_stopwatch.Elapsed.TotalSeconds;

        /// <summary>
        /// Equivalent to Unity's Time.unscaledTime - returns elapsed time in seconds since application start
        /// </summary>
        public static float unscaledTime => (float)_stopwatch.Elapsed.TotalSeconds;

        /// <summary>
        /// Returns elapsed time in milliseconds since application start
        /// </summary>
        public static long timeMilliseconds => _stopwatch.ElapsedMilliseconds;

        /// <summary>
        /// Returns the DateTime when the application started
        /// </summary>
        public static DateTime startTime => _startTime;

        /// <summary>
        /// Returns current DateTime
        /// </summary>
        public static DateTime now => DateTime.Now;

        /// <summary>
        /// Resets the time counter (for testing purposes)
        /// </summary>
        public static void Reset()
        {
            _stopwatch.Restart();
        }
    }
}
