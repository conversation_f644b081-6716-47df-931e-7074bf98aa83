using System;

namespace Tabula.PWG.MobileController
{
    /// <summary>
    /// .NET equivalent of Unity's Vector2 for console applications
    /// Provides 2D vector functionality without Unity dependencies
    /// </summary>
    [Serializable]
    public struct NetVector2 : IEquatable<NetVector2>
    {
        public float x;
        public float y;

        public NetVector2(float x, float y)
        {
            this.x = x;
            this.y = y;
        }

        // Static properties
        public static NetVector2 zero => new NetVector2(0, 0);
        public static NetVector2 one => new NetVector2(1, 1);
        public static NetVector2 up => new NetVector2(0, 1);
        public static NetVector2 down => new NetVector2(0, -1);
        public static NetVector2 left => new NetVector2(-1, 0);
        public static NetVector2 right => new NetVector2(1, 0);

        // Properties
        public float magnitude => (float)Math.Sqrt(x * x + y * y);
        public float sqrMagnitude => x * x + y * y;
        public NetVector2 normalized
        {
            get
            {
                float mag = magnitude;
                if (mag > 0.00001f)
                    return new NetVector2(x / mag, y / mag);
                return zero;
            }
        }

        // Operators
        public static NetVector2 operator +(NetVector2 a, NetVector2 b) => new NetVector2(a.x + b.x, a.y + b.y);
        public static NetVector2 operator -(NetVector2 a, NetVector2 b) => new NetVector2(a.x - b.x, a.y - b.y);
        public static NetVector2 operator *(NetVector2 a, float d) => new NetVector2(a.x * d, a.y * d);
        public static NetVector2 operator *(float d, NetVector2 a) => new NetVector2(a.x * d, a.y * d);
        public static NetVector2 operator /(NetVector2 a, float d) => new NetVector2(a.x / d, a.y / d);
        public static bool operator ==(NetVector2 lhs, NetVector2 rhs) => (lhs - rhs).sqrMagnitude < 0.0000001f;
        public static bool operator !=(NetVector2 lhs, NetVector2 rhs) => !(lhs == rhs);

        // Methods
        public void Normalize()
        {
            float mag = magnitude;
            if (mag > 0.00001f)
            {
                x /= mag;
                y /= mag;
            }
            else
            {
                x = 0;
                y = 0;
            }
        }

        public static float Dot(NetVector2 lhs, NetVector2 rhs) => lhs.x * rhs.x + lhs.y * rhs.y;
        public static float Distance(NetVector2 a, NetVector2 b) => (a - b).magnitude;
        public static NetVector2 Lerp(NetVector2 a, NetVector2 b, float t)
        {
            t = Math.Max(0f, Math.Min(1f, t));
            return new NetVector2(a.x + (b.x - a.x) * t, a.y + (b.y - a.y) * t);
        }

        // IEquatable implementation
        public bool Equals(NetVector2 other) => this == other;
        public override bool Equals(object obj) => obj is NetVector2 other && Equals(other);
        public override int GetHashCode() => x.GetHashCode() ^ (y.GetHashCode() << 2);
        public override string ToString() => $"({x:F1}, {y:F1})";

#if UNITY_ENGINE
        // Implicit conversion to/from Unity Vector2 when Unity is available
        public static implicit operator UnityEngine.Vector2(NetVector2 v) => new UnityEngine.Vector2(v.x, v.y);
        public static implicit operator NetVector2(UnityEngine.Vector2 v) => new NetVector2(v.x, v.y);
#endif
    }
}
