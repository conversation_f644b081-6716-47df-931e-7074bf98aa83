﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using System.Threading;
using Tabula.WebServices.Arguments;

// PMController version, stripped down

namespace Tabula.WebServices
{
	internal static class Constants
	{
		public const string password = "jasdj38JJSAl3HHkh3484f52kJNj8h48mrDsc2945";
	}

	public static class URL
	{
		public enum Modes
		{
			Debug = 1,
			Production =2
		}

		public static Modes Mode = Modes.Production;

		// webservices.tabulatouch.com are C# Azure Functions
		private const string ProductionBaseUrl1 = "https://webservices.tabulatouch.com/api/";
		private const string LocalBaseUrl1 = "http://localhost:7148/api/";

		private static string _base1 => (Mode == Modes.Production ? ProductionBaseUrl1 : LocalBaseUrl1);

		private const string _LicenseAuthCodeCheck = "LicenseAuthCodeCheck?code=EDCKLZUBy5p2Ty6k4PvDUXp9sodQg49Xwmf3eKUsFStKAzFu5wNckA==";
		public static string LicenseAuthCodeCheck => _base1 + _LicenseAuthCodeCheck;

		private const string _CalibrationImageUpload = "CalibrationImageUpload?code=eIXA0tgDVcAnoc08PZRF-SXyl-lgbWdHzhhq_7ZAldbRAzFutAoI1w==";
		public static string CalibrationImageUpload => _base1 + _CalibrationImageUpload;
	}

#if TABULAWEBSERVICES_CLIENT

	#region Client

	// Client class for calls
	public static class Client
	{
		public static Action<string> OnLog;
		public static Action<Exception> OnException;

		public static Resp POST<Resp>(string url, object obj, bool encrypt = true)
		{
			return POST<Resp>(url, JsonConvert.SerializeObject(obj), encrypt);
		}

		public static async Task<Resp> POSTAsync<Resp>(string url, object obj, bool encrypt = true, CancellationToken cancellationToken = default)
		{
			return await POSTAsync<Resp>(url, JsonConvert.SerializeObject(obj), encrypt, cancellationToken);
		}

		public static Resp POST<Resp>(string url, string body, bool encrypt = true)
		{
			// Use the async version but block on it for backward compatibility
			var task = POSTAsync<Resp>(url, body, encrypt, CancellationToken.None);
			return task.Result;
		}

		public static async Task<Resp> POSTAsync<Resp>(string url, string body, bool encrypt = true, CancellationToken cancellationToken = default)
		{
			var opts = new HttpUtils.HttpPostOptions
			{
				ReceiveTextResponse = true
			};

			try
			{
				var httpResult = await HttpUtils.HttpPost2Async(url, encrypt ? Serializer.EncryptString(body, Constants.password) : body, opts, cancellationToken);

				{
					// supports both an OkObjectResult (Azure C#) or a plan text (Azure Python or standard)
					string value_received = "";

					try
					{
						OkObjectResult okResult = JsonConvert.DeserializeObject<OkObjectResult>(httpResult.ReceivedText);

						if (okResult == null || okResult.StatusCode != 200)
						{
							// If it's an error it's not encrypted
							throw new Exception($"Error in call, status={okResult.StatusCode}, message={okResult.Value}");
						}

						if (!string.IsNullOrEmpty((string) okResult.Value))
							value_received = (string) okResult.Value;
						else
							value_received = httpResult.ReceivedText;
					}
					catch
					{
						value_received = httpResult.ReceivedText;
					}


					var value_decrypted = encrypt ? Serializer.DecryptString(value_received, Constants.password) : value_received;
					var response = JsonConvert.DeserializeObject<Resp>(value_decrypted);

					OnLog?.Invoke(JsonConvert.SerializeObject(response, Formatting.Indented));

					return response;
				}
			}
			catch (Exception ex)
			{
				OnException?.Invoke(ex);
			}

			return default;
		}

		public static LicenseAuthCodeCheck_Response LicenseAuthCodeCheck(string product, string auth_code)
		{
			// Use the async version but block on it for backward compatibility
			var task = LicenseAuthCodeCheckAsync(product, auth_code, CancellationToken.None);
			return task.Result;
		}

		public static async Task<LicenseAuthCodeCheck_Response> LicenseAuthCodeCheckAsync(string product, string auth_code, CancellationToken cancellationToken = default)
		{
			var req = new LicenseAuthCodeCheck_Request
			{
				product = product,
				auth_code = auth_code
			};

			var resp = await POSTAsync<LicenseAuthCodeCheck_Response>(URL.LicenseAuthCodeCheck, req, true, cancellationToken);

			return resp;
		}

		public static CalibrationImageUpload_Response CalibrationImageUpload(string product, string auth_code, string image_path)
		{
			// Use the async version but block on it for backward compatibility
			var task = CalibrationImageUploadAsync(product, auth_code, image_path, CancellationToken.None);
			return task.Result;
		}

		public static async Task<CalibrationImageUpload_Response> CalibrationImageUploadAsync(string product, string auth_code, string image_path, CancellationToken cancellationToken = default)
		{
			var req = new CalibrationImageUpload_Request()
			{
				product = product,
				auth_code = auth_code
			};

			try
			{
				// Use the static HttpClient instance from HttpUtils to prevent resource exhaustion
				// Prepare the content of the form
				using (var form = new MultipartFormDataContent())
				{
					var req_str = Serializer.EncryptString(JsonConvert.SerializeObject(req), Constants.password);

					// Add the "request" parameter to the form
					form.Add(new StringContent(req_str), "request");

					// Read the image file and add it to the form
					byte[] imageBytes = File.ReadAllBytes(image_path);
					form.Add(new ByteArrayContent(imageBytes, 0, imageBytes.Length), "image", "image.jpg");

					// Post the form to a URL using the new HttpUtils method
					string responseContent = await HttpUtils.PostMultipartAsync(URL.CalibrationImageUpload, form, cancellationToken);

					// Check the result
					OkObjectResult result = null;
					if (!string.IsNullOrEmpty(responseContent))
					{
						result = JsonConvert.DeserializeObject<OkObjectResult>(responseContent);
					}
					else
					{
						// If it's an error it's not encrypted
						throw new Exception($"Error in CalibrationImageUpload call: empty response");
					}

					if (result == null || result.StatusCode != 200)
					{
						throw new Exception($"Error in CalibrationImageUpload call, status={result?.StatusCode}, message={result?.Value}");
					}

					var value_decrypted = Serializer.DecryptString(result.Value as string, Constants.password);
					var value_obj = JsonConvert.DeserializeObject<CalibrationImageUpload_Response>(value_decrypted);

					OnLog?.Invoke(JsonConvert.SerializeObject(value_obj, Formatting.Indented));

					return value_obj;
				}
			}
			catch(Exception ex)
			{
				OnException?.Invoke(ex);
				return null;
			}
		}
	}

	#endregion

#endif
}


namespace Tabula.WebServices.Arguments
{
	#region LicenseAuthCodeCheck

	[Serializable]
	public class LicenseAuthCodeCheck_Request
	{
		public string product;
		public string auth_code;
		public bool admin;
	}

	[Serializable]
	public class LicenseAuthCodeCheck_Response
	{
		public int result;

		public const int Result_OK_NORMAL = 1;  // normal auth_code
		public const int Result_OK_ADMIN = 2;   // admin auth_code

		public const int Result_ERROR = -1;
	}

	#endregion


	#region CalibrationImageUpload

	// this will be put into a "data" parameter in the multiform upload
	[Serializable]
	public class CalibrationImageUpload_Request
	{
		public string product;
		public string auth_code;
	}

	[Serializable]
	public class CalibrationImageUpload_Response
	{
		public int result;

		public const int Result_ERROR = 0;  // generic error
		public const int Result_OK = 1;	// the upload went through
	}

	#endregion
}
