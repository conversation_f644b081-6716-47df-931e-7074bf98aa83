//TABULA_GUID:{12EFEE98-BE2F-4F1C-8B0E-774FB8A362DE}
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using System.IO;
using System;
using System.Text;
#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEditor.Build.Reporting;
#if UNITY_IOS
using UnityEditor.iOS.Xcode;
using UnityEditor.iOS.Xcode.Extensions;
#endif
#endif


namespace Tabula.Unity
{

    public class BuildProductName : MonoBehaviour
    {
        [Tooltip("Use context menu to get/set from PlayerSettings")]
        public string ProductName;
        public string ProductVersion;
        public int    BuildVersion;             // used both in Android and iOS
        public string BundleIdentifier;         // used bth in Android and iOS

        public SceneReference[] ScenesToBuild;       

        //public bool             IncrementVersionBuild = true;
        [Tooltip("Will write the version on the output folder, in a version.txt file")]
        public bool             WriteVersionToFile = true;

#if UNITY_EDITOR

        public BuildGroup Windows = new BuildGroup();
        public BuildGroup MacOS = new BuildGroup();
		public BuildGroup Android = new BuildGroup();
		public BuildGroup iOS = new BuildGroup();

		public void Reset()
        {
            if (string.IsNullOrEmpty(ProductName))
                ProductName = PlayerSettings.productName;

            if (string.IsNullOrEmpty(ProductVersion))
                ProductVersion = PlayerSettings.bundleVersion;
        }

        [ContextMenu("Get from PlayerSettings")]
        public void Get()
        {
            ProductName = PlayerSettings.productName;
            ProductVersion = PlayerSettings.bundleVersion;
            
            // other settings could be different from android and ios
        }

        [ContextMenu("Set to PlayerSettings")]
        public void Set()
        {            
            PlayerSettings.productName = ProductName;
            PlayerSettings.bundleVersion = ProductVersion;
            PlayerSettings.Android.bundleVersionCode = BuildVersion;
            PlayerSettings.iOS.buildNumber = BuildVersion.ToString();
            PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Android, BundleIdentifier);
			PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.iOS, BundleIdentifier);
		}

        // Will switch platform, add ONLY scenes in build ,and trigger build
        [ContextMenu("Build WIN")]
        public void Build_Windows() => Build(Windows);

		[ContextMenu("Build MACOS")]
		public void Build_Mac() => Build(MacOS);

		[ContextMenu("Build ANDROID")]
		public void Build_Android() => Build(Android);

		[ContextMenu("Build IOS")]
		public void Build_iOS()
        {
            var summary = Build(iOS);
            #if UNITY_IOS
            PostProcessiOS(summary);
            #endif
        }


		public BuildSummary Build(BuildGroup bg)
        {
            string _get_extension()
			{
                switch (bg.group)
				{
                    case BuildTargetGroup.Standalone:
                        switch (bg.target)
						{
                            case BuildTarget.StandaloneWindows:
                            case BuildTarget.StandaloneWindows64:
                                return ".exe";
                        }
                        break;

                    case BuildTargetGroup.Android:
                            return ".apk";

                    // TODO: iOS
				}

                return "";
			}

            UnityEngine.Debug.Log($"BUILD Started {bg.group} {bg.target} {bg.path}");

            // EditorUserBuildSettings.SwitchActiveBuildTarget(build_group, build_target);

            Set();
                        
            List<string> scenes = new List<string>();


            BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions();

            foreach (var s in ScenesToBuild)
			{
                scenes.Add(s.ScenePath);
            }

            // BUILD            

            string build_exe_path = Path.Combine(bg.path, ProductName + _get_extension());

			buildPlayerOptions.scenes = scenes.ToArray();
            buildPlayerOptions.locationPathName = build_exe_path;
            buildPlayerOptions.targetGroup = bg.group;
            buildPlayerOptions.target = bg.target;
            buildPlayerOptions.options = bg.options | BuildOptions.ShowBuiltPlayer;

            BuildReport report = BuildPipeline.BuildPlayer(buildPlayerOptions);
            BuildSummary summary = report.summary;

            if (summary.result == BuildResult.Succeeded)
            {
                UnityEngine.Debug.Log("Build succeeded: " + summary.totalSize + " bytes");

                /*
                if (IncrementVersionBuild)
                {
                    if (Version.TryParse(ProductVersion, out Version version))
                    {
                        ProductVersion = new Version(version.Major, version.Minor, version.Build != -1 ? version.Build + 1 : 0, version.Revision != -1 ? version.Revision : 0).ToString();
                        EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
                    }
                }
                */

                if (WriteVersionToFile)
                {
					//File.WriteAllText(Path.Combine(bg.path,"version.txt"),ProductVersion);

					// no CR ? // Convert the word to bytes
					File.WriteAllBytes(Path.Combine(bg.path, "version.txt"), Encoding.UTF8.GetBytes(ProductVersion));
				}

            }

            if (summary.result == BuildResult.Failed)
            {
                UnityEngine.Debug.Log("Build failed");
            }

			return summary;
        }


		// for reference iOS post-process (Nicola Torpei)
		#if UNITY_IOS

		public class XCodeCommon
		{
			public static string mainTargetGuid(PBXProject proj)
			{
				string mainTargetGuid;

				var unityMainTargetGuidMethod = proj.GetType().GetMethod("GetUnityMainTargetGuid");
				var unityFrameworkTargetGuidMethod = proj.GetType().GetMethod("GetUnityFrameworkTargetGuid");

				if (unityMainTargetGuidMethod != null && unityFrameworkTargetGuidMethod != null)
				{
					mainTargetGuid = (string)unityMainTargetGuidMethod.Invoke(proj, null);
				}
				else
				{
					mainTargetGuid = proj.TargetGuidByName("Unity-iPhone");
				}

				return mainTargetGuid;
			}

			public static string unityFrameworkTargetGuid(PBXProject proj)
			{
				string mainTargetGuid;
				string unityFrameworkTargetGuid;

				var unityMainTargetGuidMethod = proj.GetType().GetMethod("GetUnityMainTargetGuid");
				var unityFrameworkTargetGuidMethod = proj.GetType().GetMethod("GetUnityFrameworkTargetGuid");

				if (unityMainTargetGuidMethod != null && unityFrameworkTargetGuidMethod != null)
				{
					mainTargetGuid = (string)unityMainTargetGuidMethod.Invoke(proj, null);
					unityFrameworkTargetGuid = (string)unityFrameworkTargetGuidMethod.Invoke(proj, null);
				}
				else
				{
					mainTargetGuid = proj.TargetGuidByName("Unity-iPhone");
					unityFrameworkTargetGuid = mainTargetGuid;
				}

				return unityFrameworkTargetGuid;
			}
		}

		public static void PostProcessiOS(BuildSummary summary)
		{
			var projBase = summary.outputPath;
			var projPath = projBase + "/Unity-iPhone.xcodeproj/project.pbxproj";
			var proj = new PBXProject();
			proj.ReadFromFile(projPath);

			// Configure build settings
			var mainTargetGuid = XCodeCommon.mainTargetGuid(proj);
			var unityFrameworkTargetGuid = XCodeCommon.unityFrameworkTargetGuid(proj);

			foreach (string guid in new string[] { mainTargetGuid, unityFrameworkTargetGuid })
			{
				proj.SetBuildProperty(guid, "ENABLE_BITCODE", "NO");
			}

			//proj.AddFileToBuild(unityFrameworkTargetGuid, proj.AddFile("usr/lib/libsqlite3.tbd", "Frameworks/libsqlite3.tbd", PBXSourceTree.Sdk));
			//proj.AddFileToBuild(unityFrameworkTargetGuid, proj.AddFile("usr/lib/libz.tbd", "Frameworks/libz.tbd", PBXSourceTree.Sdk));

			proj.WriteToFile(projPath);

			string plistPath = projBase + "/Info.plist";
			PlistDocument plist = new PlistDocument();
			plist.ReadFromString(File.ReadAllText(plistPath));
			// Get root
			PlistElementDict rootDict = plist.root;

			/*
			var buildKey1 = "UIFileSharingEnabled";
			rootDict.SetBoolean(buildKey1, true);
			var buildKey2 = "LSSupportsOpeningDocumentsInPlace";
			rootDict.SetBoolean(buildKey2, true);
			var buildKey3 = "UIStatusBarHidden";
			rootDict.SetBoolean(buildKey3, false);
			*/

			var buildKey4 = "ITSAppUsesNonExemptEncryption";
			rootDict.SetBoolean(buildKey4, false);

			/*
			rootDict.SetString("NSPhotoLibraryUsageDescription", "Please allow access to Photo Library in order to save QR Code images");
			rootDict.SetString("NSPhotoLibraryAddUsageDescription", "Please allow access to Photo Library in order to save QR Code images");
			rootDict.SetString("NSLocationAlwaysAndWhenInUseUsageDescription", "GPS is used to track your position");
			rootDict.SetString("NSMicrophoneUsageDescription", "Microphone is used to stream audio in video streaming");
			rootDict.SetString("LSMinimumSystemVersion", "13.0");
			*/


			// Write to file
			File.WriteAllText(plistPath, plist.WriteToString());

			/*
			var entitlementsFileName = proj.GetBuildPropertyForAnyConfig(mainTargetGuid, "CODE_SIGN_ENTITLEMENTS") ?? Application.identifier + ".entitlements";
			var pcm = new ProjectCapabilityManager(projPath, entitlementsFileName, null, mainTargetGuid);
			pcm.AddBackgroundModes(BackgroundModesOptions.LocationUpdates);
            pcm.WriteToFile();
			*/

			

			UnityEngine.Debug.Log("PostprocessiOS completed");
		}
    #endif

#endif

    }

#if UNITY_EDITOR

	[Serializable]
    public class BuildGroup
    {
		public BuildTargetGroup     group = BuildTargetGroup.Standalone;
		public BuildTarget          target = BuildTarget.StandaloneWindows;
        public BuildOptions         options = BuildOptions.None;

		public string               path;
	}

#endif


}