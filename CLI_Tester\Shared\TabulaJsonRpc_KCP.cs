//TABULA_GUID:{F9A9CE69-327E-4F7F-9A29-EA7F293A24F3}
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Tabula.Log;

using kcp2k;
using System.Drawing;
using System.Collections;
using System.Linq;
using Tabula.SharedObjectMap;
using Newtonsoft.Json;
using System.IO.Pipes;

/* TabulaJsonRpc
 * 
 * Version:
 *      1.6 (28/07/18) Stripping fully-quelified types (beta)
 *      1.5 (27/06/18) Changed timeout method in connect/disconnect/call with Shared.TimeoutAfter
 *      1.4 (24/06/18) Added support for RPCResult with stream responses
 *      1.3 (07/05/18) Removed  ManagedClient.ForceDisconnectAfterCall, semantics was wrong in async, so new ManagedClient().Call().. is the right way
 *      1.2 (03/05/18) Added ManagedClient.CallTimeout and ManagedClient.ForceDisconnectAfterCall, bug enorme in Client.Disconnect()
 *      1.1 (18/12/17) RPCResult added Error
 *      1.0 stable
 * 
 * 
 * Working implementation of a RPC protocol based on JSon object with custom parameters to invoke a remote API, over a normal Socket.
 * Uses a PCL library for maximum compatibility.
 * Writes compact packets to avoid Nagle algorithm, and just WORKS!
 */

/* Defines:
 *     TABULARPC_OSC: will include OSC functionality in ManagedClient
 *     
 *     
 * Dependencies:
 *      Newtonsoft.Json
 */

#if TABULARPC_OSC
using Tabula.OscCommand;
#endif

namespace Tabula.RPC
{
    public class RPCException : Exception
    {
        public RPCException(string message) : base(message)
        { }
    }

    [JsonObject(MemberSerialization.OptIn)]
    public class RPCCall
    {
        [JsonProperty]
        public long     Id { get; set; }

        [JsonProperty]
        public string   Name { get; set; }

        [JsonProperty]
        public int      NameId { get; set; } = 0;    // alternative way of calling, based on function id instead of name

        [JsonProperty]
        public object[] Arguments { get; private set; }

        [JsonProperty]
        public bool     HasResult { get; set; }

        // Not-serialized and set by server-received header
        public bool     IsCompressed { get; set; }

        public KcpChannel Channel { get; set; } = KcpChannel.Reliable;
        
       

        [JsonProperty]
        public RPCCall[]    Batch { get; set; }

        // static for batches
        public static RPCCall CallWithResult = new RPCCall() { HasResult = true };
        public static RPCCall CallWithoutResult = new RPCCall() { HasResult = false };

        // reference to underlying socket during call processing
        /*
        [JsonIgnore]
        public ITcpSocketClient SocketClient { get; set; }
        */

        [JsonIgnore]
        public Client Client { get; set; }

        [JsonIgnore]
        public IPEndPoint RemoteEndPoint { get; set; }

        [JsonIgnore]
        public int    ConnectionId { get; set; }    // KCP_PORTING: the connection id

        /*
        [JsonIgnore]
        public IPEndPoint   RemoteEndPoint => new IPEndPoint(IPAddress.Parse(SocketClient.RemoteAddress), SocketClient.RemotePort);
        */

        // Server side check this is a client transmit call
        //public bool IsTransfer => Arguments.Length == 1 && (Arguments[0] is RPCTransfer);

        public RPCCall() { }

        private static string Lock = "lock";
        private static long GlobalID = 0;

        public static RPCCall Create(string name, bool request_result, params object[] args)
        {
            var call = new RPCCall();

            lock (Lock)
            {                
                call.Id = Interlocked.Increment(ref GlobalID);
			}

            call.Name = name;
            call.Arguments = args;
            call.HasResult = request_result;

            return call;
        }

        public static RPCCall Create(int name_id, bool request_result, params object[] args)
        {
            var call = new RPCCall();

            lock (Lock)
            {
                call.Id = Interlocked.Increment(ref GlobalID);
			}

            call.NameId = name_id;
            call.Arguments = args;
            call.HasResult = request_result;

            // Updates carry special configurations like the sending channel
            for (int i = 0; i < args.Length; i++)
                if (args[i] is GuidUpdate)
                    call.Channel = (args[i] as GuidUpdate).IsDirectSync ? KcpChannel.Unreliable : KcpChannel.Reliable;

            return call;
        }

        // Helper to convert a call arguments (JToken usually) to types, returning object array
        public static object[] ConvertArguments(RPCCall call, params System.Type[] types)
        {
            try
            {
                var converted_arguments = new object[call.Arguments.Length];

                for (int i = 0; i < call.Arguments.Length; i++)
                {
                    converted_arguments[i] = ConvertAny(call.Arguments[i], types[i]);
                }

                return converted_arguments;
            }
            catch(Exception ex)
            {
                return null;
            }
        }

        public override string ToString()
        {
            return $"RPCCall ({Id}) {(NameId!=0 ? $"nameid={NameId}" : $"name={Name}")} args#={Arguments?.Length}";
        }

        public static void ResetCallCount()
        {
            lock (Lock)
            {
                GlobalID = 0;
            }
        }

        // NOTE: these are the same in APIWrapper... but copied to avoid dependencies
        public static object ConvertAny(object value, Type type)
        {
            if (value == null)
                return null;

            object converted;

            // handles complex objects first, as well as normal primitives
            converted = ConvertFromJson(value, type);

            // other conversion (ex: int64 -> int32)
            if (converted.GetType() != type)
                converted = Convert.ChangeType(value, type);

            return converted;
        }

        public static object ConvertFromJson(object value, Type type)
        {
            if (value is JToken)
                return (value as JToken).ToObject(type);
            else
                return Convert.ChangeType(value, type);
        }
    }

    [JsonObject(MemberSerialization.OptIn)]
    public class RPCResult
    {
        [JsonProperty]
        public long Id { get; set; }  // the RpcCall Id

        [JsonProperty]
        public string ResultType { get; private set; }

        [JsonProperty]
        public object Result { get; private set; }

        [JsonProperty]
        public string Error { get; private set; }

        public static RPCResult InterBatchResult = RPCResult.Create(null, "interbatchresult"); // dummy result for calls during a batch

        [JsonIgnore]
        public RPCCall Call { get; set; }     // SERVER: reference to the call

        public RPCResult() { }

        public static RPCResult Create(RPCCall call, object r = null)
        {
            RPCResult result = new RPCResult();

            result.Call = call;
            if (call!=null)
                result.Id = call.Id;
            // NOTE: GetType() alone will return full type info which will NOT deserialize on client because of different assembly
            //       FullName returns namespace and class
            result.ResultType = r?.GetType().FullName;
            result.Result = r;
            result.Error = null;

            return result;
        }                        
        
        public static RPCResult CreateError(RPCCall call, string error)
        {
            RPCResult result = new RPCResult();

            result.Call = call;
            if (call != null)
                result.Id = call.Id;
            result.ResultType = null;
            result.Result = null;
            result.Error = error;

            return result;
        }

        public object ConvertedResult
        {
            get
            {
                if (Result == null || ResultType == null)
                    return null;

                object ret_obj = null;

                if (Result is JToken)
                {
                    ret_obj = (Result as JToken).ToObject(Shared.GetTypeAllAssemblies(ResultType));
                }
                else
                {
                    // SPECIAL CASE: byte[] are "sometimes" returned as string (Base64)?
                    if (ResultType == "System.Byte[]" && Result is string)
					{
                        Result = Convert.FromBase64String(Result as string);
                    }

                    // A conversion is needed for primitive types
                    ret_obj = Convert.ChangeType(Result, Shared.GetTypeAllAssemblies(ResultType));
                }

                return ret_obj;
            }
        }

        public override string ToString()
        {
            //return string.Format("RPCResult ({0}) = {1}", Id, Result.ToString());
            // return string.Format("RPCResult ({0})", Id);
            return $"RPCResult id:{Id} type:{ResultType} value:{Result?.ToString()}" + (!string.IsNullOrEmpty(Error) ? $" error:{Error}" : "");
        }		        
    }

    public static class Constants
    {
		// connect or disconnect
		public static int ConnectTimeout = 10000;

        // protocol inernals (KCP)
        public static int ProtocolTimeout = 10000;   // was 3000

        // message queue
        public static int QueueMessageTimeout = 10000;              // was 1000
        public static int QueueMessageCleanupInterval = 10000;      // was 1000
        public static int QueueReceiveTimeout = 10000;              // was 3000

		// RPC Calls
		public static int CallTimeout = 10000;                      // was 5000

		public static void Setup()
        {
#if DEBUG
			// debug timeouts
			ConnectTimeout = 1000 * 1000;
            ProtocolTimeout = 1000 * 1000;
            QueueMessageTimeout = 1000 * 1000;
            QueueMessageCleanupInterval = 1000 * 1000;
            QueueReceiveTimeout = -1;
            CallTimeout = 0;
#endif
        }
	}

    // Queue for receiving messages from server
	public class KcpMessageQueue: IDisposable
	{
		public TimeSpan MessageTimeout;
        public int      MessageCleanupInterval;    

        public class InternalQueue : ConcurrentDictionary<long, ConcurrentBag<Shared.KcpMessage>>
        {

        }

		// messages are indexed by id
		//private ConcurrentDictionary<long, ConcurrentBag<Shared.KcpMessage>> _messages;

        private ConcurrentDictionary<int, InternalQueue> _messages;

		private CancellationTokenSource _cts_cleanup;

		public KcpMessageQueue()
		{
            _messages = new ConcurrentDictionary<int, InternalQueue>();
			_cts_cleanup = new CancellationTokenSource();
			_start_cleanup_task();
        }

		public KcpMessageQueue(Shared.KcpMessage m) : base()
		{
            Enqueue(m);
		}

		public KcpMessageQueue(List<Shared.KcpMessage> messages) : base()
		{
            foreach (var m in messages)
                Enqueue(m);
		}

		public void Enqueue(Shared.KcpMessage msg)
		{
            msg.header.time = DateTime.Now.Ticks;

            if (!_messages.ContainsKey(msg.header.code))
            {
                // new msg specific code
                _messages.TryAdd(msg.header.code, new InternalQueue());
            }

            _messages[msg.header.code].AddOrUpdate(
				msg.header.msg_id,
				key => new ConcurrentBag<Shared.KcpMessage> { msg },
				(key, existingBag) =>
				{
					existingBag.Add(msg);
					return existingBag;
				});
		}

        // tries to get messages with requested id, if chuncked will wait for ALL of them to be available
        public async Task<List<Shared.KcpMessage>> TryDequeue(int msg_code, long msg_id, int timeout=-1, int internal_wait=5)
        {
            var retmsg = new List<Shared.KcpMessage>();

            var time_timeout = (timeout==-1 ? DateTime.MaxValue : DateTime.Now.AddMilliseconds(timeout));

            while (true)
            {
                if (DateTime.Now >= time_timeout)
                    throw new  TimeoutException("KcpMessageQueue.TryGet() timeout");

                if (_messages.ContainsKey(msg_code))
                {
                    var _internal_queue = _messages[msg_code];

                    if (_internal_queue.ContainsKey(msg_id))
                    {
                        if (_internal_queue[msg_id].ElementAt(0).header.chunk_count == 1)
                        {
                            // single message
                            retmsg.Add(_internal_queue[msg_id].ElementAt(0));

                            while (!_internal_queue.TryRemove(msg_id, out ConcurrentBag<Shared.KcpMessage> b))
                            {
                                if (DateTime.Now >= time_timeout)
                                    throw new TimeoutException("KcpMessageQueue.TryGet() timeout msg remove");

                                await Task.Delay(internal_wait);
                            }

                            return retmsg;
                        }
                        else
                        {
                            // multiple chunks wait for that number, then reorder
                            uint chunks = _internal_queue[msg_id].ElementAt(0).header.chunk_count;

                            while (retmsg.Count < chunks)
                            {
                                if (DateTime.Now >= time_timeout)
                                    throw new TimeoutException("KcpMessageQueue.TryGet() timeout msg remove multiple chunk");

                                if (_internal_queue[msg_id].TryTake(out Shared.KcpMessage m))
                                    retmsg.Add(m);
                                else
                                    await Task.Delay(internal_wait);
                            }

                            // clear the dictionary entry
                            while (!_internal_queue.TryRemove(msg_id, out ConcurrentBag<Shared.KcpMessage> b))
                            {
                                if (DateTime.Now >= time_timeout)
                                    throw new TimeoutException("KcpMessageQueue.TryGet() timeout msg remove multiple chunk");

                                await Task.Delay(internal_wait);
                            }

                            // order messages
                            retmsg = retmsg.OrderBy(message => message.header.chunk_sequence).ToList();

                            return retmsg;
                        }
                    }
                }

                await Task.Delay(internal_wait);
            }
        }

		private void _start_cleanup_task()
		{
			Task.Run(async () =>
			{
				var cancellationToken = _cts_cleanup.Token;

                var to_prune = new List<long>();
				while (!cancellationToken.IsCancellationRequested)
				{                                       
					foreach (var kvp in _messages)
					{
						to_prune.Clear();

						foreach (var kvp2 in kvp.Value)
                        {
                            // if ANY message of a certain id is timet out, then all the messages must be pruned
                            bool timedout = false;
                            foreach (var m in kvp2.Value)
                            {
                                var dt_time = new DateTime(m.header.time);
                                if (DateTime.Now > (dt_time + MessageTimeout))
                                {
                                    timedout = true;
                                    break;
                                }
                            }

                            if (timedout)
                            {
                                to_prune.Add(kvp2.Key);
                                continue;
                            }
                        }


						foreach (var i in to_prune)
                            _messages[kvp.Key].TryRemove(i, out ConcurrentBag<Shared.KcpMessage> v);
					}


					await Task.Delay(1000, cancellationToken); // Adjust the delay as needed
				}
			}, _cts_cleanup.Token);
		}

		private void _stop_cleanup_task()
		{
			_cts_cleanup.Cancel();
		}

		public void Dispose()
		{
            _stop_cleanup_task();
		}
	}


	public class Client
    {
        public bool Compressed { get; set; }    // deprecate ?

        public IPEndPoint ip_endpoint;

        private KcpClient client;
        public  KcpClient NativeClient => client;

        // Events
        public Action OnConnected;
		public Action OnDisconnected;
        public Action<Shared.KcpMessage> OnDataReceived;                                    // if set will NOT enqueue received messages to be interpreted as server results
		public Action<kcp2k.KcpChannel, Shared.KcpMessage> OnCustomKcpMessageReceived;     // higher level intercept custom messages (not RPC)
		public Action<kcp2k.ErrorCode, string> OnError;

		public Func<Shared.FileTransfer, bool>   OnFileTransferRequest; // incoming file transfer, return false to deny it
		public Action<Shared.FileTransfer, bool> OnFileTransferEnded;   // result and outcome of an incoming file transfer


		// Generic Queue for RPCResult and server updates
		// They will be stored indexed by id for results, and as 0 for server updates
		public KcpMessageQueue Queue { get; private set; }

        public long ReceivedAcks = 0;

		public Client()
        {
			Constants.Setup();

			Compressed = false;
        }

		#region KCP Specific

		private CancellationTokenSource? _tokenSource;
		private Task _kcp_client_update_task;

		private async Task KcpClientTickAsync(CancellationToken token)
		{
			while (!token.IsCancellationRequested)
			{
				await Task.Delay(20);
				client?.Tick();
			}
		}

		private async Task<Shared.JSonReceiveResult<T>> WaitAndReceiveJsonObject<T>(RPCCall call, int timeout = -1)
        {
            try
            {
                // Monitors the queue for result messages with the call id
                var messages = await Queue.TryDequeue(Shared.KCPMSG_RPC, call.Id, timeout);

                if (messages == null)
					throw new Exception("WaitAndReceiveJsonObject extracted null message from queue");

				Shared.KcpMessage final_msg = null;

				if (messages.Count == 1)
                {
					// single chunk
					final_msg = messages[0];
                }
                else
                {
                    // multiple chunks
                    final_msg = new Shared.KcpMessage();
					for (int i = 0; i < messages.Count; i++)
                        final_msg.AppendPayload(messages[i]);
                }

                // Final decode payload as json

				var ret = new Shared.JSonReceiveResult<T>() { Object = final_msg.DecodePayloadAsJson<T>() };
				if (ret.Object == null)
                    throw new Exception("WaitAndReceiveJsonObject decoded json is null");

				return ret;
			}
            catch (Exception ex)
            {
				Logger.DefaultLog.logException($"WaitAndReceiveJsonObject: rpc_call_id={call.Id} EXCEPTION",ex);

				return null;
			}
		}

        #endregion

		public async Task<bool> Connect(string address, int port, CancellationToken token=default)
        {
			// Create the queue
			Queue?.Dispose();

			Queue = new KcpMessageQueue()
			{
				MessageTimeout = TimeSpan.FromMilliseconds(Constants.QueueMessageTimeout),
				MessageCleanupInterval = Constants.QueueMessageCleanupInterval
			};

			KcpConfig config = new KcpConfig()
            {
				DualMode = false,
                Timeout = Constants.ProtocolTimeout,
                SendWindowSize = 255,
				ReceiveWindowSize = 255,
                SendBufferSize = 1024 * 1024 * 14,
                RecvBufferSize = 1024 * 1024 * 14
			};

			client = new KcpClient(
				() =>
				{
					// OnConnected
					Logger.DefaultLog.WriteLine($"[CLIENT KCP] Connected");
                    OnConnected?.Invoke();
                },
                (message, channel) =>
                {
					// OnData
					// Logger.DefaultLog.WriteLine($"[CLIENT KCP] Received back {message.Count} Bytes");

                    Shared.KcpMessage msg = null;

                    try
                    {
                        msg = Shared.KcpMessage.Decode(message, channel);

						if (msg == null)
							return;

						// Logger.DefaultLog.WriteLine($"MSG RECEIVED: code={msg.header.code} id={msg.header.msg_id} {msg.header.chunk_sequence}/{msg.header.chunk_count} {msg.header.payload_total_size}");

                        if (OnDataReceived != null)
                        {
                            // custom handling
                            OnDataReceived.Invoke(msg);
                        }
                        else
                        {
                            if (channel == KcpChannel.Reliable)
                            {
                                switch (msg.header.code)
                                {
                                    case Shared.KCPMSG_RPC:
                                    case Shared.KCPMSG_FILE_TRANSFER_C2S_INFO:  // server -> client must accept
										Queue.Enqueue(msg);
                                        break;

									case Shared.KCPMSG_ACK: 
                                        Interlocked.Increment(ref ReceivedAcks); 
                                        break;

									case Shared.KCPMSG_FILE_TRANSFER_S2C_INFO:
                                        // incoming file transfer, authoritative, client cannot deny it (just ignore it)
										_Client_FileTransferRequest(msg);
										break;

									case Shared.KCPMSG_FILE_TRANSFER_S2C_DATA:
                                        // ongoing transfer, 
										_Client_FileTransferData(msg);
										break;


									default:
										
                                        // Custom processing
										if (OnCustomKcpMessageReceived != null)
										{
											OnCustomKcpMessageReceived?.Invoke(KcpChannel.Reliable, msg);
										}
										else
											throw new FormatException($"Tabula.RPC.Client.OnData() unknown (reliable) message code: {msg.header.code}");
										break;
								}
                            }
                            else
                            {
								// same handler for unreliable messages ?

								// Custom processing
								if (OnCustomKcpMessageReceived != null)
								{
									OnCustomKcpMessageReceived?.Invoke(KcpChannel.Unreliable, msg);
								}
								else
									throw new FormatException($"Tabula.RPC.Client.OnData() unknown (unreliable) message code: {msg.header.code}");

							}
                        }
                    }
                    catch(Exception ex)
                    {
                        Logger.DefaultLog.logException("Tabula.RPC.Client on message received", ex);
                    }
				},
				() => 
                {
					// OnDisconnected
					Logger.DefaultLog.WriteLine($"[CLIENT KCP] Disconnected");

                    _tokenSource?.Cancel();
                    _kcp_client_update_task = null;

					OnDisconnected?.Invoke();
				},
				(error, reason) =>
                {
					// OnError
					Logger.DefaultLog.WriteLine($"[CLIENT KCP] OnClientError({error}, {reason}");
                    OnError?.Invoke(error, reason);
                }, config);


			// for canceling kcp internal update loop
			_tokenSource = new CancellationTokenSource();

		    // TODO: timeout?
		    client.Connect(address, (ushort) port);

            ip_endpoint = new IPEndPoint(IPAddress.Parse(address), port);

			_kcp_client_update_task = Task.Run(() => KcpClientTickAsync(_tokenSource.Token));

            // TODO: timeout ?
            try
            {
                Stopwatch sw = Stopwatch.StartNew();
                while (!client.connected)
                {
                    token.ThrowIfCancellationRequested();

                    if (Constants.ConnectTimeout != -1)
                        if (sw.ElapsedMilliseconds > Constants.ConnectTimeout)
                        {
                            Disconnect();
                            return false;
                        }

                    await Task.Delay(10);
                }
            }
            catch(OperationCanceledException ex)
            {
                return false;
            }

            return true;
        }

        public void Disconnect()
        {
            if (client == null)
                return;

			Queue?.Dispose();

			//TODO: timeout
			_tokenSource.Cancel();
            client.Disconnect();
        }

        public bool IsConnected => client!=null && client.connected;

        // Standard Call, with or without result depends on HasResult property
        // Result is null if error in sending
        // Result is valid if Object is not null.
        public async Task<RPCResult> Call(RPCCall call, KcpChannel channel = KcpChannel.Reliable)
        {
            if (Shared.Debug)			
                Tabula.Log.Logger.DefaultLog.WriteLine($"Tabula.RPC.Client.Call() {JsonConvert.SerializeObject(call)}");               			
           

			// KCP_PORTING: if an unreliable send is requested, no need for a result
			if (call.HasResult && channel == KcpChannel.Reliable)
            {
                Shared.SendRPCCall_KCPClient(client, call, channel);

                // KCP specific: await the reception, will happen on this client
                // NOTE: this should really be improved with matching id etc..
                Shared.JSonReceiveResult<RPCResult> ret = await WaitAndReceiveJsonObject<RPCResult>(call, Constants.QueueReceiveTimeout);

				if (ret == null)
                    return null;
                else
                {                   
                    RPCResult rpcresult = ret.Object as RPCResult;

                    return ret.Object;
                }

            }
            else
            {
                // Just send, without waiting

				Shared.SendRPCCall_KCPClient(client, call, channel);
				return RPCResult.Create(call);
            }
        }

        public async Task<RPCResult> CallBatch(RPCCall[] batch, bool get_results)
        {
            RPCCall batchcall = RPCCall.Create("batch", get_results);
            batchcall.Batch = batch;

            RPCResult result = await Call(batchcall);

            return result;
        }

		// timeout is used for setup phase as well as ACK waiting
		// Returns: 
		// 1: ok
		// -1: cannot find the file
		// -2: timeout setting up transfer
		// -3: socket no more connected
		// -4: timeout in ack waiting
		// -5: wrong read bytes count
        // -6: server denied
        // -10: general exception
		public async Task<int> TransferFile(FileInfo file_info, int timeout=-1)
		{
			byte[] buffer = new byte[1024 * 50];
			uint count = 0;
			int read = 0;

			if (!File.Exists(file_info.FullName))
			{
				Logger.DefaultLog.WriteLine($"Tabula.RPC.Client.TransferFile() cannot find file:{file_info.FullName}");
				return -1;
			}

			uint bytes_transmitted = 0;
            uint client_sent_chuncks = 0;

            // Send request
            Shared.FileTransfer file_transfer = new Shared.FileTransfer()
            {
                filename = file_info.Name,
                size = file_info.Length,
                mimetype = ""   // TODO
            };

            Shared.FileTransfer file_transfer_accepted = null;

			try
            {
                Shared.SendJsonObject_KCPClient(NativeClient, file_transfer, KcpChannel.Reliable, Shared.KCPMSG_FILE_TRANSFER_C2S_INFO, 0);

                // wait for a reply of type KCPMSG_FILE_TRANSFER_INFO 
                var messages = await Queue.TryDequeue(Shared.KCPMSG_FILE_TRANSFER_C2S_INFO, 0, timeout);

                var file_transfer_msg = messages[0];
                file_transfer_accepted = file_transfer_msg.DecodePayloadAsJson<Shared.FileTransfer>();

                // check if it was accepted
                if (file_transfer_accepted.accepted_transfer_id == -1)
                {
                    // denied
                    return -6;
                }
            }
            catch(Exception ex)
            {
                // timeout or something else
                return -2;
            }

			try
			{
				using (var filestream = File.OpenRead(file_info.FullName))
				{
					while ((read = await filestream.ReadAsync(buffer, 0, buffer.Length)) > 0)
					{
						count += (uint)read;

						if (!NativeClient.connected)
						{							
							Logger.DefaultLog.WriteLine("Tabula.RPC.Client.TransferFile() Socket is not connected anymore!");
                            return -3;
						}

                        var prev_acks = ReceivedAcks;

						client_sent_chuncks++;

						Shared.SendDataChunk_KCPClient(NativeClient, new ArraySegment<byte>(buffer, 0, read).ToArray(), client_sent_chuncks, 0, (uint)file_info.Length, KcpChannel.Reliable, Shared.KCPMSG_FILE_TRANSFER_C2S_DATA, file_transfer_accepted.accepted_transfer_id);
						
						bytes_transmitted += count;
						

						Logger.DefaultLog.WriteLine($"Tabula.RPC.Client.TransferFile() sent_chunks={client_sent_chuncks} bytes_transmitted={bytes_transmitted}");

                        // stop-wait for each single message sent to be acknowledged
                        var time_wait_acks = DateTime.Now;
						while (ReceivedAcks <= prev_acks)
						{
							await Task.Delay(5);

                            if (timeout > 0)
                                if ((DateTime.Now - time_wait_acks).TotalMilliseconds > timeout)
                                {
								    Logger.DefaultLog.WriteLine("Tabula.RPC.Client.TransferFile() timeout waiting for ack");
								    return -4;
							    }
						}

						if (count > file_info.Length)
						{
							filestream.Close();
							Logger.DefaultLog.WriteLine($"Tabula.RPC.Client.TransferFile() filename={file_info.FullName} read wrong number of bytes");
                            return -5;
						}
						else if (count == file_info.Length)
							break;
					}

					filestream.Close();
				}
			}
			catch (Exception ex)
			{
				Logger.DefaultLog.logException("Tabula.RPC.Client.TransferFile()", ex);
				return -10;
			}

			// Get the file incremental Id
			// TODO: suspended
			/*
			var h = await Shared.ReceiveHeader(ReadStream);
			var ret = await Shared.ReceiveJsonObject<int>(h, WriteStream, ReadStream);
			int file_id = ret.Object;

			return file_id;
            */

			return 1;

		}


		#region Server -> Client file transfer

		private ConcurrentDictionary<long, Shared.FileTransfer> client_file_transfers = new ConcurrentDictionary<long, Shared.FileTransfer>();

		// Server request for a file, client can abort only not sending ACK back
		private void _Client_FileTransferRequest(Shared.KcpMessage msg)
		{
			Shared.FileTransfer file_transfer = null;

			try
			{
				file_transfer = msg.DecodePayloadAsJson<Shared.FileTransfer>();

				if (OnFileTransferRequest != null)
				{
					var ret = OnFileTransferRequest?.Invoke(file_transfer);
					if (ret == false)
					{
						// deny just ignoring it
						return;
					}
				}

				if (file_transfer.size <= 0)
				{
					// ERR: Bad DataLength
					Logger.DefaultLog.WriteLine($"IncomingTransfer: bad stream size!");
					return;
				}

				file_transfer.written_bytes = 0;

				// File is received a a temporary file name
				file_transfer.temp_file_path = Path.GetTempFileName();

				file_transfer.filestream = File.OpenWrite(file_transfer.temp_file_path);
			}
			catch (Exception ex)
			{
				Logger.DefaultLog.logException($"Transfer exception", ex);
				return;
			}

			// server will alwys valorize the transfer id, since it is authoritative						
			file_transfer.written_bytes = 0;
			file_transfer.chunk_last_index = 0;

			// create an entry to keep track
			client_file_transfers.TryAdd(file_transfer.accepted_transfer_id, file_transfer);

			Logger.DefaultLog.WriteLine($"Begin S2C Transfer, connection={file_transfer.connection_id} size={file_transfer.size}");

			// From now on we can expect data packets in OnFileTransferData()
		}

		// TODO: implement timeout on data received as well as pruning stale file transfers
		private void _Client_FileTransferData(Shared.KcpMessage msg)
		{
			if (msg.header.code != Shared.KCPMSG_FILE_TRANSFER_S2C_DATA)
				return;

			if (!client_file_transfers.TryGetValue(msg.header.msg_id, out Shared.FileTransfer file_transfer))
			{
				Logger.DefaultLog.WriteLine($"Transfer id={msg.header.msg_id} not valid");
				return;
			}

			// received chunk must be > than the last one
			if (msg.header.chunk_sequence <= file_transfer.chunk_last_index)
			{
				_Client_FileTransferEnd(file_transfer, false);
				Logger.DefaultLog.WriteLine($"Transfer id={file_transfer.accepted_transfer_id} out of sequence chunk, aborting");
				return;
			}

			file_transfer.filestream.Write(msg.payload);
			file_transfer.written_bytes += msg.header.payload_chunk_size;
			file_transfer.chunk_last_index = msg.header.chunk_sequence;
			file_transfer.time_last_received = DateTime.Now;

			Logger.DefaultLog.WriteLine($"Transfer id={file_transfer.accepted_transfer_id} write, size={msg.header.payload_chunk_size}, total={file_transfer.size}");

			// Send ACK
			// TODO: particular acks?
			Shared.SendACK_KCPClient(NativeClient, KcpChannel.Reliable);

			if (file_transfer.written_bytes == file_transfer.size)
			{
				_Client_FileTransferEnd(file_transfer, true);
				return;
			}
			else if (file_transfer.written_bytes > file_transfer.size)
			{
				_Client_FileTransferEnd(file_transfer, false);
				throw new RPCException($"Transfer id={file_transfer.accepted_transfer_id} size={msg.header.payload_chunk_size}, total={file_transfer.size} received wrong number of bytes");
			}
		}

		private void _Client_FileTransferEnd(Shared.FileTransfer file_transfer, bool success)
		{
			file_transfer.filestream?.Close();
			client_file_transfers.TryRemove(file_transfer.accepted_transfer_id, out Shared.FileTransfer removed_filetransfer);

			if (success)
			{
				// User event, can copy the temporary file
				OnFileTransferEnded?.Invoke(file_transfer, success);
			}
		}

		#endregion
	}

	public class Server
    {       
        public KcpServer server;
        public KcpServer NativeServer => server;

        public Action<int, IPEndPoint>         OnClientConnected;      //KCP_PORTING: made the arg an IPEndPoint
        public Action<int, IPEndPoint>         OnClientDisconnected;   // doesn't mean a real socket disconnectio, but the end of the processing

        public Action<Shared.KcpMessage>        OnCustomKcpMessageReceived;     // higher level intercept custom messages (not RPC)

        public Func<RPCCall, (RPCResult result, bool interrupt)>              OnCallReceived;         //KCP_PORTING: intercepts call early to process special messages and can interrupt real processing -> apicall
        public Func<RPCCall, RPCResult>         OnProcessCall;
        public Action<Exception>                OnServerException;

		public Func<Shared.FileTransfer, bool>   OnFileTransferRequest; // incoming file transfer, return false to deny it
		public Action<Shared.FileTransfer, bool> OnFileTransferEnded;   // result and outcome of an incoming file transfer

        public int ConnectionsInProgress = 0;
        public int ServerExceptions { get; private set; }

        public long ReceivedAcks  = 0;

        public ConcurrentDictionary<int, KcpMessageQueue> Queues { get; private set; }


        public Server()
        {
            Constants.Setup();

            // passthrough
            OnProcessCall = (call) =>
            {
                if (call == null)
                    return null;

                // NOTE: this way only api methods will be waited (await) on the client for a return!
                if (!call.HasResult)
                    return null;

                return RPCResult.Create(call);
            };
        }

        public static int received_acks = 0;

        // Returns the creation state as well a the final port
        public async Task<Tuple<bool, int>> StartServer(int port, int tries = 1, int tries_wait=1000, int port_increment = 10)
        {
            return await Task.Run(() =>
            {
                int i = 0;
                while (i < tries)
                {
                    try
                    {
                        // TODO: expose timeout, in KCP case it is the max time between two messages received
                        int timeout = 3000;

#if DEBUG
                        timeout = 120 * 1000;
#endif

						// disable info for now
						kcp2k.Log.Info = (s) => Logger.DefaultLog.WriteLine($"INFO: {s}");
						kcp2k.Log.Warning = (s) => Logger.DefaultLog.WriteLine($"WARNING: {s}");
						kcp2k.Log.Error = (s) => Logger.DefaultLog.WriteLine($"ERROR: {s}");

						KcpConfig config = new KcpConfig()
                        {
                            DualMode = false,
                            Timeout = timeout,
							SendWindowSize = 255,
							ReceiveWindowSize = 255,
							SendBufferSize = 1024 * 1024 * 14,
							RecvBufferSize = 1024 * 1024 * 14,

                            MaxTimeReceiving = 15   // TABULA ADDED!
						};

                        server = new KcpServer(
                            (connectionId) =>
                            {
                                // TODO: Deny a connection here if there are too many?
                                // Can we do it here?

                                // OnConnected
                                //Logger.DefaultLog.WriteLine($"[SERVER KCP] Received a connection_id={connectionId}");

                                _onClientConnected(connectionId, server.GetClientEndPoint(connectionId));
                            },
                            (connectionId, message, channel) =>
                            {
								// OnData
								// Logger.DefaultLog.WriteLine($"[SERVER KCP] [{connectionId} , {channel}] Received data ({message.Count} Bytes)");                              

                                try
                                {
                                    // decodes and assigns server-specific info
                                    var msg = Shared.KcpMessage.Decode(message, channel, connectionId);

                                    if (msg == null)
                                        return;

                                    // At this level only RPC messages are processe, everything else is passed to the higher level
									switch (msg.header.code)
									{
										case Shared.KCPMSG_RPC: onKCPMessageReceived(msg); break;

                                        case Shared.KCPMSG_ACK: Interlocked.Increment(ref ReceivedAcks); break;

                                        case Shared.KCPMSG_FILE_TRANSFER_C2S_INFO:
                                            // incoming file transfer, should authorize it
                                            _Server_FileTransferRequest(msg);
                                            break;

                                        case Shared.KCPMSG_FILE_TRANSFER_C2S_DATA:
                                            // ongoing transfer, 
                                            _Server_FileTransferData(msg);
                                            break;

                                        default:

                                            // Custom processing
                                            if (OnCustomKcpMessageReceived != null)
                                            {
                                                OnCustomKcpMessageReceived?.Invoke(msg);
                                            }
                                            else
											    throw new FormatException($"Tabula.RPC.Server.OnData() unknown message code: {msg.header.code}");
                                            break;
									}

                                }
                                catch(Exception ex)
                                {
                                    Logger.DefaultLog.logException("Tabula.RPC.Server.OnData() exception", ex);
                                }
                                
                            },
                            (connectionId) =>
                            {
								// OnDisconnected
								//Logger.DefaultLog.WriteLine($"[SERVER KCP] Disconnected connection_id={connectionId}");

								_onClientDisconnected(connectionId, server.GetClientEndPoint(connectionId));
							},
                            (connectionId, error, reason) =>
                            {
								// OnError
								Logger.DefaultLog.WriteLine($"[SERVER KCP]: OnServerError({connectionId}, {error}, {reason}");

								_onClientDisconnected(connectionId, server.GetClientEndPoint(connectionId));
							},

                            config);

                        // for canceling kcp internal update loop
                        _kcp_server_task_tokensource = new CancellationTokenSource();
                        _kcp_server_task_token = _kcp_server_task_tokensource.Token;

                        Task.Run(async () =>
						{
                            while (!_kcp_server_task_token.IsCancellationRequested)
                            {
                                server.Start((ushort)port);

                                // The task will also check if the server is still running and return false if there are exceptions or server socket is invalid
                                _kcp_server_task = Task.Run(() => KcpServerTickAsync(_kcp_server_task_tokensource.Token));

                                // await for server end (cancellation or fault)
                                bool server_ret = await _kcp_server_task;

                                if (!server_ret)
								{
									// the server has faulted!
									Logger.DefaultLog.WriteLine($"[SERVER KCP]: SERVER TASK FAULTED ! RESTARTING");
                                    await Task.Delay(100);
								}
                                else
                                {
									Logger.DefaultLog.WriteLine($"[SERVER KCP]: SERVER TASK EXITED");
                                    return;
								}
                            }

                        });


                        return new Tuple<bool, int>(true, port);
                    }
                    catch (Exception ex)
                    {
                        Logger.DefaultLog.logException("Server.StartServer()", ex);
                    }

                    port += port_increment;
                    i++;
                }

                return new Tuple<bool, int>(false, -1);
            });
        }

        #region KCP Specific

		private CancellationTokenSource? _kcp_server_task_tokensource;
        private CancellationToken _kcp_server_task_token;

		private Task<bool> _kcp_server_task;
        
		private async Task<bool> KcpServerTickAsync(CancellationToken token)
		{
			while (!token.IsCancellationRequested)
			{
                try
                {
                    await Task.Delay(10);
                    server?.Tick();

                    if (server!=null)
                    {
                        if (!server.IsActive())
                        {
							Logger.DefaultLog.Log($"Tabula.RPC.Server.KcpServerTickAsync() server polled as not active!");
                            throw new Exception("Server polled as not active");
						}
                    }
                }
                catch(Exception ex)
                {
                    var is_active = server.IsActive();
					Logger.DefaultLog.logException($"Tabula.RPC.Server.KcpServerTickAsync() EXCEPTION IsActive={is_active}", ex);

                    if (!is_active)
                    {
                        // the server seems to be dead
                        return false;
                    }                    
				}
			}

            return true;
		}

		#endregion

		public async Task StopServer()
        {
            try
            {
                _kcp_server_task_tokensource?.Cancel();
                bool server_ret = await _kcp_server_task;

                if (!server_ret)
                {
					Logger.DefaultLog.Log($"Tabula.RPC.Server.StopServer() server task returned false while canceling task");
				}
            }
            catch(Exception ex)
            {
				Logger.DefaultLog.logException($"Tabula.RPC.Server.StopServer() EXCEPTION while canceling task", ex);
			}

			server?.Stop();

            _kcp_server_task = null;
            _kcp_server_task_tokensource = null;
			_kcp_server_task_token = default;

			server = null;
        }

        // Use this only to simulate internal crashes, as it does not gracefully close
        public void KillServer()
        {
            server?.Stop();
        }

        // NOTE: The server _NEVER_ closes a connection, it is the Client responsibility
        // KCP_PORTING: changed the signature, we just get IPEndPoint not socketclient
        private void _onClientConnected(int connectionId, IPEndPoint endpoint)
        {
            lock (server)
            {
                // these are local
                Interlocked.Increment(ref ConnectionsInProgress);
            }

            OnClientConnected?.Invoke(connectionId, endpoint);
        }

        private void _onClientDisconnected(int connectionId, IPEndPoint endpoint)
		{
			lock (server)
			{
				// these are local
				Interlocked.Decrement(ref ConnectionsInProgress);               
			}

			OnClientDisconnected?.Invoke(connectionId, endpoint);
		}
   

		// KCP_PORTING: this was part of onConnectionReceived() in original lib
		private void onKCPMessageReceived(Shared.KcpMessage msg)
		{
			try
			{
                // NOTE: Server now receives only unchunked messages, as calls are "light" (like GET no POST)
                if (msg.header.code != Shared.KCPMSG_RPC || msg.header.chunk_count > 1)
                    throw new Exception("onKCPMessageReceived: server doesn't support chunks in receiving calls, or wrong message type");

				var call = new Shared.JSonReceiveResult<RPCCall>() { Object = msg.DecodePayloadAsJson<RPCCall>() };

				if (call == null || call.Object == null)
					throw new Exception("onKCPMessageReceived: cannot decode as a json object");
				
                // get a reference to the current server and
                call.Object.ConnectionId = msg.header.connection_id;
                call.Object.RemoteEndPoint = server.GetClientEndPoint(msg.header.connection_id);

                // Early intercepts call before API to optionally process special messages
                if (OnCallReceived != null)
                {
                    var ret = OnCallReceived.Invoke(call.Object);

                    // can send returns also!
                    // TODO: still useful ? what code/msgid ?
                    if (ret.result != null)
                        Shared.SendJsonObject_KCPServer(server, ret.result, msg.header.connection_id, msg.header.channel);

                    if (ret.interrupt)
                        return;
                }

				RPCResult result = null;

				// TODO: maximum time in processing call??

				try
				{
					result = OnProcessCall?.Invoke(call.Object);					
				}
				catch (Exception ex)
				{
					Logger.DefaultLog.logException("Server.onKCPMessageReceived() processing call", ex);
				}

				if (call.Object.HasResult && result != null)
				{
                    // will send the result as a chunked message if necessary
                    // header will contain the result id (initial call id)
                    Shared.SendJsonObject_KCPServer(server, result, msg.header.connection_id, msg.header.channel, Shared.KCPMSG_RPC, result.Id);                       					
				}				
			}
			catch (System.IO.IOException ex)
			{
				// This can also be the remote Socket closing, so let's terminate the socket
				// Logger.DefaultLog.logException("Server.onConnectionReceived()", ex);
			}
			catch (Exception ex)
			{
				// TODO: terminate on too many errrors?
				OnServerException?.Invoke(ex);
				lock (server)
					ServerExceptions++;
			}
		}

		#region Client -> Server file tranfer

		private int server_file_transfer_id = 0;
        private ConcurrentDictionary<long, Shared.FileTransfer> server_file_transfers = new ConcurrentDictionary<long, Shared.FileTransfer>();

        private void _Server_FileTransferRequest(Shared.KcpMessage msg)
        {
            Shared.FileTransfer file_transfer = null;

            try
            {
                file_transfer = msg.DecodePayloadAsJson<Shared.FileTransfer>();

                if (OnFileTransferRequest!=null)
                {
                    var ret = OnFileTransferRequest?.Invoke(file_transfer);
                    if (ret == false)
                    {
                        file_transfer.accepted_transfer_id = -1;    // deny
						Shared.SendJsonObject_KCPServer(NativeServer, file_transfer, msg.header.connection_id, KcpChannel.Reliable, Shared.KCPMSG_FILE_TRANSFER_C2S_INFO, 0);
                        return;
					}
                }

				if (file_transfer.size <= 0)
				{
					// ERR: Bad DataLength
					Logger.DefaultLog.WriteLine($"IncomingTransfer: bad stream size!");
					return;
				}				               

				file_transfer.written_bytes = 0;

				// File is received a a temporary file name
				file_transfer.temp_file_path = Path.GetTempFileName();

				file_transfer.filestream = File.OpenWrite(file_transfer.temp_file_path);                				
			}
            catch(Exception ex)
            {
				Logger.DefaultLog.logException($"Transfer exception", ex);
                return;
			}

			// accept
			file_transfer.accepted_transfer_id = Interlocked.Increment(ref server_file_transfer_id);
            file_transfer.connection_id = msg.header.connection_id;
			file_transfer.written_bytes = 0;
            file_transfer.chunk_last_index = 0;

			// reply to accept it (id 0)
			Shared.SendJsonObject_KCPServer(NativeServer, file_transfer, msg.header.connection_id, KcpChannel.Reliable, Shared.KCPMSG_FILE_TRANSFER_C2S_INFO, 0);

			// create an entry to keep track
			server_file_transfers.TryAdd(file_transfer.accepted_transfer_id, file_transfer);

			Logger.DefaultLog.WriteLine($"Begin Transfer, connection={file_transfer.connection_id} size={file_transfer.size}");

			// From now on we can expect data packets in OnFileTransferData()
		}

        // TODO: implement timeout on data received as well as pruning stale file transfers
		private void _Server_FileTransferData(Shared.KcpMessage msg)
        {
            if (msg.header.code != Shared.KCPMSG_FILE_TRANSFER_C2S_DATA)
                return;

            if (!server_file_transfers.TryGetValue(msg.header.msg_id, out Shared.FileTransfer file_transfer))
            {
				Logger.DefaultLog.WriteLine($"Transfer  id={msg.header.msg_id} not valid");
				return;
            }

            // received chunk must be > than the last one
            if (msg.header.chunk_sequence <= file_transfer.chunk_last_index)
            {
                _FileTransferEnd(file_transfer, false);
				Logger.DefaultLog.WriteLine($"Transfer id={file_transfer.accepted_transfer_id} out of sequence chunk, aborting");
				return;
			}

            file_transfer.filestream.Write(msg.payload);
            file_transfer.written_bytes += msg.header.payload_chunk_size;
            file_transfer.chunk_last_index = msg.header.chunk_sequence;
            file_transfer.time_last_received = DateTime.Now;

			Logger.DefaultLog.WriteLine($"Transfer id={file_transfer.accepted_transfer_id} write, size={msg.header.payload_chunk_size}, total={file_transfer.size}");

            // Send ACK
            // TODO: particular acks?
            Shared.SendACK_KCPServer(NativeServer, file_transfer.connection_id, KcpChannel.Reliable);

			if (file_transfer.written_bytes == file_transfer.size)
			{
                _FileTransferEnd(file_transfer, true);
				return;
			}
			else if (file_transfer.written_bytes > file_transfer.size)
			{
				_FileTransferEnd(file_transfer, false);
				throw new RPCException($"Transfer id={file_transfer.accepted_transfer_id} size={msg.header.payload_chunk_size}, total={file_transfer.size} received wrong number of bytes");
			}
		}

        private void _FileTransferEnd(Shared.FileTransfer file_transfer, bool success)
        {		
			file_transfer.filestream?.Close();
            server_file_transfers.TryRemove(file_transfer.accepted_transfer_id, out Shared.FileTransfer removed_filetransfer);

            if (success)
            {
                // User event, can copy the temporary file
                OnFileTransferEnded?.Invoke(file_transfer, success);
            }
        }

		#endregion

		#region Server -> Client file transfer

		// timeout is used for setup phase as well as ACK waiting
		// Returns: 
		// 1: ok
		// -1: cannot find the file
		// -4: timeout in ack waiting
		// -5: wrong read bytes count
		// -10: general exception
		public async Task<int> TransferFile(FileInfo file_info, int connectionId, int timeout = -1)
		{
			byte[] buffer = new byte[1024 * 50];
			uint count = 0;
			int read = 0;

			if (!File.Exists(file_info.FullName))
			{
				Logger.DefaultLog.WriteLine($"Tabula.RPC.Server.TransferFile() cannot find file:{file_info.FullName}");
				return -1;
			}

			uint bytes_transmitted = 0;
			uint server_sent_chuncks = 0;

			// Send request, with a generate transfer id, authoritative, client cannot deny
			Shared.FileTransfer file_transfer = new Shared.FileTransfer()
			{
				filename = file_info.Name,
				size = file_info.Length,
				accepted_transfer_id = Interlocked.Increment(ref server_file_transfer_id),
			    mimetype = ""   // TODO
			};

			
            Shared.SendJsonObject_KCPServer(NativeServer, file_transfer, connectionId, KcpChannel.Reliable, Shared.KCPMSG_FILE_TRANSFER_S2C_INFO, 0);


			try
			{
				using (var filestream = File.OpenRead(file_info.FullName))
				{
					while ((read = await filestream.ReadAsync(buffer, 0, buffer.Length)) > 0)
					{
						count += (uint)read;						

						var prev_acks = ReceivedAcks;

						server_sent_chuncks++;

						Shared.SendDataChunk_KCPServer(NativeServer, connectionId, new ArraySegment<byte>(buffer, 0, read).ToArray(), server_sent_chuncks, 0, (uint)file_info.Length, KcpChannel.Reliable, Shared.KCPMSG_FILE_TRANSFER_S2C_DATA, file_transfer.accepted_transfer_id);

						bytes_transmitted += count;

						Logger.DefaultLog.WriteLine($"Tabula.RPC.Server.TransferFile() sent_chunks={server_sent_chuncks} bytes_transmitted={bytes_transmitted}");

						// stop-wait for each single message sent to be acknowledged
						var time_wait_acks = DateTime.Now;
						while (ReceivedAcks <= prev_acks)
						{
							await Task.Delay(5);

							if (timeout > 0)
								if ((DateTime.Now - time_wait_acks).TotalMilliseconds > timeout)
								{
									Logger.DefaultLog.WriteLine("Tabula.RPC.Server.TransferFile() timeout waiting for ack");
									return -4;
								}
						}

						if (count > file_info.Length)
						{
							filestream.Close();
							Logger.DefaultLog.WriteLine($"Tabula.RPC.Server.TransferFile() filename={file_info.FullName} read wrong number of bytes");
							return -5;
						}
						else if (count == file_info.Length)
							break;
					}

					filestream.Close();
				}
			}
			catch (Exception ex)
			{
				Logger.DefaultLog.logException("Tabula.RPC.Server.TransferFile()", ex);
				return -10;
			}

			return 1;

		}

		#endregion

	}

	//KCP_PORTING: an interface is indeed handy, now needed for the temporary implementation ofn TransmitFileStream_KCP()
	public interface IManagedClient
    {
        Task<RPCResult> Call(RPCCall call);
        Task<Client>    GetClient(CancellationToken token);
	}

    // JsonRpcManagedClient client will manage the connection and persistence, with heuristics for maximum performance
    // It also supports OSC sending if TABULARPC_OSC is defined
    // KCP_PORTING_SINGLE_SOCKET: only uses a persistent KcpClient, and no pool
    public class ManagedClient : IDisposable, IManagedClient
    {       
        private List<RPCCall>   batch = null;

        public string           Address { get; private set; }
        public int              Port { get; private set; }

        public Tabula.RPC.Client Client { get; private set; }

        // Base events, in order to receive them early, all others should be queried on the RPC.Client after is created/connected
		public Action           OnConnected;
		public Action           OnDisconnected;

		public ManagedClient()
        { }

		#region OSC

#if !TABULARPC_OSC
        public ManagedClient(string address, int port)
        {
            Create(address, port);
        }

        public ManagedClient Clone() => new ManagedClient(Address, Port);

        public ManagedClient Create(string address, int port)
        {
            Address = address;
            Port = port;

            return this;
        }
#else
		// OSC

		public int  OSCPort { get; private set; }
        internal    OscCommand.Client OSC;
        private     List<Tuple<string,object[]>> osc_batch = null;

        public ManagedClient(string address, int port, int osc_port=-1, int osc_retry=1)
        {
            Create(address, port, osc_port, osc_retry);
        }

        public ManagedClient Clone() => new ManagedClient(Address, Port, OSCPort, OSC!=null ? OSC.RetryCount : 1);

        public ManagedClient Create(string address, int port, int osc_port=-1, int osc_retry=1)
        {
            Address = address;
            Port = port;

            OSCPort = osc_port;
            if (osc_port != -1)
            {
                OSC = new OscCommand.Client(Address, OSCPort);
                OSC.RetryCount = osc_retry;
            }
            
#if DEBUG
            CallTimeout = 180 * 1000;
#endif

            return this;
        }

		public void BeginOSCBatch()
        {
            osc_batch = new List<Tuple<string, object[]>>();
        }

        public bool IsOSCBatch
        {
            get
            {
                return osc_batch != null;
            }
        }

        public void AddOSCBatch(string method, params object[] args)
        {
            if (osc_batch == null)
                return;

            osc_batch.Add(new Tuple<string, object[]>(method, args));
        }

        public void CallOSCBatch()
        {
            if (osc_batch == null)
                return;

            OSC.sendCommandBatch(osc_batch);

            osc_batch = null;
        }

#endif
		#endregion
		        
        public async Task<Client> GetClient(CancellationToken token=default)
        {
            if (Client != null)
                return Client;

            Client = new Client();

            Client.OnConnected += OnConnected;
			Client.OnDisconnected += OnDisconnected;

			if (Client.IsConnected)
				return Client;

            try
            {
                bool ret = await Client.Connect(Address, Port, token: token);
                if (!ret)
                    Client = null;
            }
			catch (Exception ex)
			{
                Logger.DefaultLog.logException("ManagedClient.GetClient()", ex);
				Client = null;
			}

            return Client;
		}


		private async Task<bool> beforeCall(RPCCall call)
        {
            var client = await GetClient();

            call.Client = client;

            return (client != null);
        }

        private async Task afterCall(RPCCall call, RPCResult result)
        {}

        public async Task<RPCResult> Call(RPCCall call)
        {
            if (Shared.Debug)
                Logger.DefaultLog.WriteLine($"RPCCall({call.Id}): BEGIN {(call.NameId != 0 ? $"id:{call.NameId} {call.Name}" : call.Name)}");

            // If batch is active accumulate
            if (batch!=null)
            {
                batch.Add(call);

                if (Shared.Debug)
                    Logger.DefaultLog.WriteLine($"RPCCall({call.Id}): BATCH ADDED");

                return RPCResult.InterBatchResult;
            }

            if (!await beforeCall(call))
            {
                if (Shared.Debug)
                    Logger.DefaultLog.WriteLine($"RPCCall({call.Id}): ERROR NO CONNECTION");

                return null;    // No connection
            }

            RPCResult result = null;

            try
            {
                // KCP_PORTING: if channel is unreliable, do not use timeout
                try
                {
                    if (Constants.CallTimeout == 0 || !call.HasResult || call.Channel == KcpChannel.Unreliable)
                        result = await call.Client.Call(call, call.Channel);
                    else
                        result = await Shared.TimeoutAfter(call.Client.Call(call, call.Channel), Constants.CallTimeout);
                }
                catch(TimeoutException)
                {
                    throw new RPCException("RPCCall: timed-out!");
                }                

            }    
            catch(Exception ex)
            {
                if (Shared.Debug)
                    Logger.DefaultLog.WriteLine($"RPCCall({call.Id}): EXCEPTION = {ex.Message}");
            }

            afterCall(call, result);

            if (Shared.Debug)
                Logger.DefaultLog.WriteLine($"RPCCall({call.Id}): END {(call.NameId != 0 ? $"id:{call.NameId} {call.Name}" : call.Name)} = {(result?.ConvertedResult ?? "null")}");

            return result;
        }

#if !__MOBILE__

        // sending files
        /*
        public async Task<bool> SendFile(string filename, int transfer_user_type)
        {
            bool ret = false;
            using (FileStream input = File.OpenRead(filename))
            {
                RPCTransfer transfer = RPCTransfer.CreateFromFile(filename, transfer_user_type);

                ret = await client.SendStream(transfer, input);
            }

            return ret;
        }

        // TODO: receiving files
        public async Task<bool> ReceiveFile(string filename, int transfer_user_type)
        {
            bool ret = false;
            using (FileStream input = File.OpenRead(filename))
            {
                RPCTransfer transfer = RPCTransfer.CreateFromFile(filename, transfer_user_type);

                ret = await client.SendStream(transfer, input);
            }

            return ret;
        }
        */


#endif


		public void BeginBatch()
        {            
            batch = new List<RPCCall>();            
        }

        public void ClearBatch()
        {
            batch = null;
        }

        public async Task<object[]> CallBatch(bool get_results)
        {
            try
            {

                if (batch == null)
                    return null;

                if (!await beforeCall((get_results ? RPCCall.CallWithResult : RPCCall.CallWithoutResult)))
                {
                    batch = null;
                    return null;    // No connection
                }

                RPCResult result = null;

                var client = await GetClient();

                try
                {
                    result = await client.CallBatch(batch.ToArray(), get_results);
                }
                catch (Exception ex)
                {
                    // Handle any kind of socket exception, missing connection or changed client
                    throw ex;
                }

                await afterCall((get_results ? RPCCall.CallWithResult : RPCCall.CallWithoutResult), result);

                if (get_results)
                {
                    // The result must be an array of results
                    try
                    {
                        batch = null;

                        RPCResult[] results_array = (RPCResult[])(result.Result as JToken).ToObject(typeof(RPCResult[]));
                        var converted_results = new object[results_array.Length];
                        for (int i = 0; i < results_array.Length; i++)
                        {
                            converted_results[i] = results_array[i].ConvertedResult;
                        }

                        return converted_results;
                    }
                    catch
                    {
                        batch = null;
                        return null;
                    }
                }
                else
                {
                    batch = null;
                    return new object[] { };
                }
            }
            catch(Exception ex)
            {
                batch = null;
                throw ex;
            }
        }

        //

        public void Dispose()
        {
            if (Client != null)
            {                
				Client?.Disconnect();

				Client.OnConnected -= OnConnected;
				Client.OnDisconnected -= OnDisconnected;

                Client = null;
			}
		}
    }

    public static class Shared
    {
        public static bool Debug = false;
        
        // wrapper , deprecate?
		public class JSonReceiveResult<T>
		{
			public T Object = default(T);       // null means error
			public bool IsCompressed = false;
		}

        // client request
        
        public class FileTransfer
        {
            public string filename;
            public string mimetype;
            public long   size;

            // server response
            public int    accepted_transfer_id;

            // runtime data
            [JsonIgnore]
            public FileStream   filestream;
            [JsonIgnore]
            public string       temp_file_path;
			[JsonIgnore]
			public long         written_bytes;
            [JsonIgnore]
            public int          connection_id;
            [JsonIgnore]
            public DateTime     time_last_received;
			[JsonIgnore]
			public uint         chunk_last_index;

			const int CANCEL_TIMEOUT = 3000;

		}

		// KCP_PORTING:
		public static int MAX_KCP_MESSAGE_SIZE = 110000;

        public static string AsUTF8String(this byte[] bs)
        {
            return Encoding.UTF8.GetString(bs, 0, bs.Length);
        }

        public static byte[] AsUTF8ByteArray(this string s)
        {
            return Encoding.UTF8.GetBytes(s);
        }

        public static string AsJson(this object o)
        {
            return JsonConvert.SerializeObject(o);
        }

        public static byte[] AsUTF8JsonByteArray(this object o)
        {
            return o.AsJson().AsUTF8ByteArray();
        }

        // Will resolve typenames to Type also from other assemblies
        public static Type GetTypeAllAssemblies(string type)
        {
            // TEST: stripping fully-qualified types to avoid assembly loading
            //       looking for a comma?
            if (type.Contains(","))
            {
                int start = type.IndexOf(',');
                int end = type.IndexOf(']');

                if (end != -1)
                {
                    var str_to_strip = type.Substring(start, (end - start));
                    type = type.Replace(str_to_strip, "");
                }
                else
                {
                    type = type.Substring(0, start - 1);
                }                
            }

            // Local Assembly first
            var t_local = Type.GetType(type, false);
            if (t_local != null)
                return t_local;

            // All other assemblies (but local one)
            var executing_assembly = Assembly.GetExecutingAssembly();

            foreach (Assembly a in AppDomain.CurrentDomain.GetAssemblies())
            {
                if (a.Equals(executing_assembly))
                    continue;

                var t_external = a.GetType(type, false);
                if (t_external != null)
                    return t_external;
            }

            return null;
        } 

        private static int _get_message_chunks(object obj, List<KcpMessage> messages, int message_code = 0, long message_id = 0)
        {
			byte[] obj_bytes = obj.AsUTF8JsonByteArray();

			// get one or more messages
			return KcpMessage.GetMessages(obj_bytes, messages, message_code, message_id);
		}

        // If message is too big returns false
		public static void SendJsonObject_KCPServer(
            KcpServer server, 
            object obj, 
            int connectionId, 
            KcpChannel channel,
			int message_code = 0,
            long message_id = 0)
		{
			var messages = new List<KcpMessage>();

			_get_message_chunks(obj, messages, message_code, message_id);

			// NOTE: no ACK because they should be small and not so many
			foreach (var m in messages)
			{
                lock(server)
                { 
                    server?.Send(connectionId, new ArraySegment<byte>(m.ToByteArray()), channel);
                }
			}
		}

		public static void SendDataChunk_KCPServer(KcpServer server, int connectionId, byte[] data, uint chunk_sequence, uint chunk_count, uint payload_total_size, KcpChannel channel, int message_code = 0, long message_id = 0)
		{
			var m = new KcpMessage()
			{
				header = new KcpMessage._header()
				{
					code = message_code,
					msg_id = message_id,
					chunk_sequence = chunk_sequence,
					chunk_count = chunk_count,
					payload_chunk_size = (uint)data.Length,
					payload_total_size = payload_total_size
				},
				payload = data
			};

			lock (server)
			{
				server?.Send(connectionId, new ArraySegment<byte>(m.ToByteArray()), channel);
			}
		}

		public static void SendACK_KCPServer(KcpServer server, int connectionId, KcpChannel channel)
		{
            var m = new KcpMessage();
            m.header.code = KCPMSG_ACK;
            m.header.chunk_count = 0;
			m.header.chunk_sequence = 0;
            m.header.payload_total_size = 0;
            m.header.payload_chunk_size = 0;
            m.payload = null;

            lock (server)
            {
                server?.Send(connectionId, new ArraySegment<byte>(m.ToByteArray()), channel);
            }
		}


		public static void SendRPCCall_KCPClient(KcpClient client, RPCCall call, KcpChannel channel)
		{
            var messages = new List<KcpMessage>();

            _get_message_chunks(call, messages, Shared.KCPMSG_RPC, call.Id);

            // NOTE: no ACK because they should be small and not so many
            foreach (var m in messages)
            {
                lock (client)
                {
                    client?.Send(new ArraySegment<byte>(m.ToByteArray()), channel);
                }
			}
		}

		public static void SendJsonObject_KCPClient(KcpClient client, object obj, KcpChannel channel, int message_code = 0, long message_id = 0)
		{
			var messages = new List<KcpMessage>();

			_get_message_chunks(obj, messages, message_code, message_id);

			foreach (var m in messages)
			{
				lock (client)
				{
					client?.Send(new ArraySegment<byte>(m.ToByteArray()), channel);
				}
			}
		}

		public static void SendDataChunk_KCPClient(KcpClient client, byte[] data, uint chunk_sequence, uint chunk_count, uint payload_total_size, KcpChannel channel, int message_code = 0, long message_id = 0)
		{
			var m = new KcpMessage()
			{
				header = new KcpMessage._header()
				{
					code = message_code,
					msg_id = message_id,
					chunk_sequence = chunk_sequence,
					chunk_count = chunk_count,
					payload_chunk_size = (uint) data.Length,
					payload_total_size = payload_total_size
				},
				payload = data
			};

			lock (client)
			{
				client?.Send(new ArraySegment<byte>(m.ToByteArray()), channel);
			}
		}


		public static void SendACK_KCPClient(KcpClient client, KcpChannel channel)
		{
			var m = new KcpMessage();
			m.header.code = KCPMSG_ACK;
			m.header.chunk_count = 0;
			m.header.chunk_sequence = 0;
			m.header.payload_total_size = 0;
			m.header.payload_chunk_size = 0;
			m.payload = null;

            lock (client)
            {
                client?.Send(new ArraySegment<byte>(m.ToByteArray()), channel);
            }
		}

        // Server Updates (inclues server commands) toggle
		public static void SendServerUpdatesToggle_KCPClient(KcpClient client, bool receive_updates)
		{
			var m = new KcpMessage();
            m.header.code = (receive_updates ? KCPMSG_RCV_UPDATES_ENABLE : KCPMSG_RCV_UPDATES_DISABLE);
			m.header.chunk_count = 0;
			m.header.chunk_sequence = 0;
			m.header.payload_total_size = 0;
			m.header.payload_chunk_size = 0;
			m.payload = null;

            lock (client)
            {
                client?.Send(new ArraySegment<byte>(m.ToByteArray()), KcpChannel.Reliable);
            }
		}

        // Ongly server Commands toggle
		public static void SendServerCommandsToggle_KCPClient(KcpClient client, bool receive_commands)
		{
			var m = new KcpMessage();
			m.header.code = (receive_commands ? KCPMSG_RCV_COMMANDS_ENABLE : KCPMSG_RCV_COMMANDS_DISABLE);
			m.header.chunk_count = 0;
			m.header.chunk_sequence = 0;
			m.header.payload_total_size = 0;
			m.header.payload_chunk_size = 0;
			m.payload = null;

			lock (client)
			{
				client?.Send(new ArraySegment<byte>(m.ToByteArray()), KcpChannel.Reliable);
			}
		}

        // KCP_PORTING: decodes directly from data received
		public static JSonReceiveResult<T> ReceiveJsonObject<T>(ArraySegment<byte> message)
		{
            // TODO: zero emssage error?

            // TODO: check for too-big data ?

            // FIXME: first character is strange?!?
            string serialized_object = Encoding.UTF8.GetString(message.Array, message.Offset, message.Count);

			T obj;

			try
			{
				obj = JsonConvert.DeserializeObject<T>(serialized_object);
			}
			catch (Exception ex)
			{
				// Problem receiving
				obj = default(T);
			}

			return new JSonReceiveResult<T>() { Object = obj };
		}

        public static Task<T> TimeoutAfter<T>(this Task<T> task, int millisecondsTimeout)
        {
            var timedTask = Task.Factory.StartNew(() =>
            {
                var completed = task.Wait(millisecondsTimeout);
                if (!completed)
                {
                    throw new TimeoutException();
                }
                return task.Result;
            });
            return timedTask;
        }

        public static Task TimeoutAfter(this Task task, int millisecondsTimeout)
        {
            var timedTask = Task.Factory.StartNew(() =>
            {
                var completed = task.Wait(millisecondsTimeout);
                if (!completed)
                {
                    throw new TimeoutException();
                }
                return task;
            });
            return timedTask;
        }

        // KCP

        // message codes
        public const int KCPMSG_RPC = 1;            // a standard KCP message containing a json payload for an RPC call or result
		public const int KCPMSG_SRV_UPDATE = 2;     // updates sent from server to client

        public const int KCPMSG_RCV_UPDATES_ENABLE = 10;    // ask the server to start send updates + commands to the client
		public const int KCPMSG_RCV_UPDATES_DISABLE = 11;   // ask the server to stop send updates + commands to the client

        public const int KCPMSG_RCV_COMMANDS_ENABLE = 12;   // ask the server to send only commands to the client
		public const int KCPMSG_RCV_COMMANDS_DISABLE = 13;  // ask the server to stop send only commands to the client

        //  client -> server
		public const int KCPMSG_FILE_TRANSFER_C2S_INFO = 50;    // generic file transfer, the payload will contain a serialized FileTransfer as well as id
        public const int KCPMSG_FILE_TRANSFER_C2S_DATA = 51;    // data of the file transfer

		// server -> client
		public const int KCPMSG_FILE_TRANSFER_S2C_INFO = 60;    // generic file transfer, the payload will contain a serialized FileTransfer as well as id
		public const int KCPMSG_FILE_TRANSFER_S2C_DATA = 61;    // data of the file transfer

		public const int KCPMSG_ACK = 99;                   // general acks

		[StructLayout(LayoutKind.Sequential, Pack =1)]
		public class KcpMessage
        {
			[StructLayout(LayoutKind.Sequential, Pack = 1)]
			public class _header
            {
                public int  code;                 // message code
                public long msg_id;               // unique id identifier (taken from RPCCall etc.)
                public uint chunk_sequence;       // the chunk sequence (starts from 1)
                public uint chunk_count;          // the chunk total count
                public uint payload_chunk_size;   // size of the payload chunk
                public uint payload_total_size;   // size of the total payload

                // extra (to valorized when received)
                public int          connection_id;
                public KcpChannel   channel;
                public long         time;       // time received (for timeouts), is the DateTime.Ticks
            }

            public _header              header = new _header();
            public byte[]               payload;

            public const int MAX_PAYLOAD_CHUNK_SIZE = 1024 * 110;   // more than this size will result in multiple chunks

			public static KcpMessage Decode(ArraySegment<byte> data, KcpChannel channel = KcpChannel.Reliable, int connection_id=-1)
			{
				KcpMessage msg = new KcpMessage();
				msg.header = new KcpMessage._header();

				try
                {
                    // NOTE: we NEED to transform ArraySegment to byte[] otherwise the memory will be rewritten!
                    // TODO: could avoid double copying in payload, just keep the whole raw-data and offset
                    var bytes = data.ToArray();

                    GCHandle handle = GCHandle.Alloc(bytes, GCHandleType.Pinned);
                    Marshal.PtrToStructure(handle.AddrOfPinnedObject(), msg.header);
                    handle.Free();

                    msg.header.channel = channel;
                    msg.header.connection_id = connection_id;

                    // copy the payload
                    if (msg.header.payload_chunk_size > 0)
                    {
                        msg.payload = new byte[msg.header.payload_chunk_size];
                        Array.Copy(bytes, Marshal.SizeOf(msg.header), msg.payload, 0, msg.payload.Length);
                    }

                    return msg;
                }
                catch (ArgumentException)
                {
					Log.Logger.DefaultLog.Log("KcpMessage.Decode() ArgumentException (length)");
                    return null;
				}
                catch(Exception ex)
                {
                    Log.Logger.DefaultLog.logException("KcpMessage.Decode()", ex);
                    return null;
				}
			}

			// Converts the whole message to a byte array, ready for sending
			public byte[] ToByteArray()
            {
				byte[] byteArray = new byte[Marshal.SizeOf(header) + (payload!=null ? payload.Length : 0)];
				GCHandle handle = GCHandle.Alloc(byteArray, GCHandleType.Pinned);
				Marshal.StructureToPtr(header, handle.AddrOfPinnedObject(), false);
				handle.Free();

                // copy the rest?
                if (payload!=null && payload.Length>0)
                    Array.Copy(payload, 0, byteArray, Marshal.SizeOf(header), payload.Length);

                return byteArray;
			}

			public T DecodePayloadAsJson<T>()
			{
				string serialized_object = Encoding.UTF8.GetString(payload, 0, payload.Length);

				T obj;

				try
				{
					obj = JsonConvert.DeserializeObject<T>(serialized_object);
				}
				catch (Exception ex)
				{
                    Log.Logger.DefaultLog.logException($"DecodePayloadAsJson() failed, serialized_object={serialized_object}", ex);

					// Problem receiving
					obj = default(T);
				}

				return obj;
			}

            // appends a payload from another message (chunk), allocates the payload
            // NOTE: messages must be valid, same code etc..
            public bool AppendPayload(KcpMessage other)
            {
                // header is reset to a 1 message
                header.code = other.header.code;
                header.chunk_count = 1;
                header.payload_chunk_size = other.header.payload_total_size;
                header.payload_total_size = other.header.payload_total_size;

                if (payload == null || payload.Length < header.payload_total_size)
                { 
                    payload = new byte[header.payload_total_size];
                    header.chunk_sequence = 0;  // NOTE: use the chunk sequence for temporary register payload advance
                }

				// copy this piece
				Array.Copy(other.payload, 0, payload, header.chunk_sequence, other.payload.Length);
				header.chunk_sequence += (uint) other.payload.Length;

                // detect last
                if (other.header.chunk_sequence == other.header.chunk_count)
                {
                    header.chunk_sequence = 1; // restore normal behaviour
                    return true;
                }
                else
                    return false;
			}

			// Returns one or more messages in chunk
			public static int GetMessages(byte[] payload, List<KcpMessage> messages, int message_code=0, long message_id=0)
            {
                if (payload.Length < MAX_PAYLOAD_CHUNK_SIZE)
                {
                    // single message
                    var m = new KcpMessage()
                    {
                        header = new _header()
                        {
                            code = message_code,
                            msg_id = message_id,
                            chunk_sequence = 1,
                            chunk_count = 1,
                            payload_chunk_size = (uint)payload.Length,
							payload_total_size = (uint) payload.Length
                        },
                        payload = payload
                    };

                    messages.Add(m);
                    return 1;
                }
                else
                {
                    // multiple chunks
                    //uint chunk_count = (uint) Math.Ceiling((double) ((double) payload.Length / (double) MAX_PAYLOAD_CHUNK_SIZE));
                    uint chunk_count = ((uint) payload.Length + MAX_PAYLOAD_CHUNK_SIZE - 1) / MAX_PAYLOAD_CHUNK_SIZE;

					uint payload_advance = 0;
					for (uint i=0; i<chunk_count; i++)
                    {
						var m = new KcpMessage()
						{
							header = new _header()
							{
								code = message_code,
								msg_id = message_id,
								chunk_sequence = i+1,
								chunk_count = chunk_count,
                                payload_total_size = (uint) payload.Length
							}
						};

                        if (i == (chunk_count-1))
                        {
                            // last
                            m.header.payload_chunk_size = (uint) payload.Length - payload_advance;
						}
                        else
                        {
                            m.header.payload_chunk_size = MAX_PAYLOAD_CHUNK_SIZE;
                        }

                        m.payload = new byte[m.header.payload_chunk_size];
                        Array.Copy(payload, payload_advance, m.payload, 0, m.header.payload_chunk_size);

                        payload_advance += m.header.payload_chunk_size;

						messages.Add(m);
					}

                    return messages.Count;
                }

            }
		}
    
    }
}

namespace Tabula
{
    // Helpers
    public static class BlockingCollectionEx
    {
        public async static Task<T> TakeAsync<T>(this BlockingCollection<T> bc, CancellationToken token, int inner_delay = 10)
        {
            // will block until available
            while (!token.IsCancellationRequested)
            {
                if (bc.TryTake(out T el))
                    return el;
                else
                    await Task.Delay(inner_delay);
            }

            throw new OperationCanceledException();
        }

		public async static Task<T> TakeAsync<T>(this ConcurrentQueue<T> bc, CancellationToken token, int inner_delay = 10)
		{
			// will block until available
			while (!token.IsCancellationRequested)
			{
				if (bc.TryDequeue(out T el))
					return el;
				else
					await Task.Delay(inner_delay);
			}

			throw new OperationCanceledException();
		}
	}
}