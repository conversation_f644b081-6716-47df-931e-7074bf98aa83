﻿//TABULA_GUID:{161B5C77-4D58-4993-825C-AB4CC47ECB1A}
/*
Copyright 2015 Get Wrecked B.V. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using System.Threading;
using System.Diagnostics;

/// <summary>
/// A thread-safe class which holds a queue with actions to execute on the next Update() method. It can be used to make calls to the main thread for
/// things such as UI Manipulation in Unity. It was developed for use in combination with the Firebase Unity plugin, which uses separate threads for event handling
/// </summary>
public class UnityMainThreadDispatcher : MonoBehaviour {

	public int MaxTimePerExecution = 30;	// max time in milliseconds for execution of commands in queue, in order not to stall Update

	// This action can be "awaited" on the event on a normal thread
	// useful to get a result back
	public interface IAsyncAction
	{
		public void Execute();
		public object WaitResult();
	}

	public class AsyncAction<T> : IAsyncAction
	{
		private SemaphoreSlim semaphore;
		private T result = default(T);
		private Func<T> func;

		public AsyncAction(Func<T> _func)
		{
			func = _func;
			semaphore = new SemaphoreSlim(0,1);
		}

		public void Execute()
		{
			result = func.Invoke();
			semaphore.Release();
		}

		public object WaitResult()
		{
			semaphore.Wait();
			return result;
		}
	}


	private static readonly Queue<Action> _executionQueue = new Queue<Action>();

    private static readonly List<Tuple<float,Action>> _timedExecutionQueue = new List<Tuple<float, Action>> ();

	private static readonly Queue<IAsyncAction> _asyncactionQueue = new Queue<IAsyncAction>();

	private Stopwatch sw = new Stopwatch ();
	public void Update() 
    {
		sw.Restart();

		lock(_executionQueue) 
        {
            // immediate queue
			while (_executionQueue.Count > 0) 
            {
				_executionQueue.Dequeue().Invoke();

				if (sw.Elapsed.TotalMilliseconds > MaxTimePerExecution)
					return;
			}

			// async queue
			while (_asyncactionQueue.Count > 0)
			{
				_asyncactionQueue.Dequeue().Execute();

				if (sw.Elapsed.TotalMilliseconds > MaxTimePerExecution)
					return;
			}

            // delayed queue
            // walk backwards to allow a safe remove
            for (int i = _timedExecutionQueue.Count - 1; i >= 0; i--)
            {
                var item = _timedExecutionQueue[i];
                if (Time.time >= item.Item1)
                {
                    item.Item2.Invoke();
                    _timedExecutionQueue.RemoveAt(i);

					if (sw.Elapsed.TotalMilliseconds > MaxTimePerExecution)
						return;
				}
            }
		}
	}

	/// <summary>
	/// Locks the queue and adds the IEnumerator to the queue
	/// </summary>
	/// <param name="action">IEnumerator function that will be executed from the main thread.</param>
	public void Enqueue(IEnumerator action, float time=-1) {
		lock (_executionQueue) 
        {
            if (time >= 0)
            {
                // enqueue for the future
                _timedExecutionQueue.Add(new Tuple<float, Action>(time, () => StartCoroutine(action)));
            }
            else
            {
                _executionQueue.Enqueue(() => StartCoroutine(action));
            }
			
		}
	}

    /// <summary>
    /// Locks the queue and adds the Action to the queue
    /// </summary>
    /// <param name="action">function that will be executed from the main thread.</param>
    // Panda: enqueue for a time in the future
    public void Enqueue(Action action, float time=-1)
    {
        Enqueue(ActionWrapper(action), time);
    }

	public void Enqueue(IAsyncAction action)
	{
		_asyncactionQueue.Enqueue(action);
	}

	public T EnqueueAndWait<T>(IAsyncAction action)
	{
		Instance().Enqueue(action);
		T result = (T) action.WaitResult();
		return result;
	}

	public T EnqueueAndWait<T>(Func<T> func)
	{
		var async_action = new UnityMainThreadDispatcher.AsyncAction<T>(func);

		Instance().Enqueue(async_action);
		T result = (T)async_action.WaitResult();
		return result;
	}

	IEnumerator ActionWrapper(Action a)
	{
		a();
		yield return null;
	}

	public IEnumerator WaitQueueIsEmptyCR()
	{
		while (true)
		{
			if (_executionQueue.Count == 0 && _timedExecutionQueue.Count == 0)
				yield break;

			yield return null;
		}
	}

	public async Task WaitQueueIsEmpty()
	{
		while (true)
		{
			if (_executionQueue.Count == 0 && _timedExecutionQueue.Count == 0)
				return;

			await Task.Delay(16);
		}
	}


	private static UnityMainThreadDispatcher _instance = null;

	public static bool Exists() {
		return _instance != null;
	}

	public static UnityMainThreadDispatcher Instance() {
		if (!Exists ()) {
			throw new Exception ("UnityMainThreadDispatcher could not find the UnityMainThreadDispatcher object. Please ensure you have added the MainThreadExecutor Prefab to your scene.");
		}
		return _instance;
	}


	void Awake() {
		if (_instance == null) {
			_instance = this;
			DontDestroyOnLoad(this.gameObject);
		}
	}

	void OnDestroy() {
			_instance = null;
	}


}
