//TABULA_GUID:{38371A39-A7B7-4BC9-9BA6-1A013FEA7256}
using System;
using System.Text;
using System.IO;

// Logger: very simple logger class, with event to be linked to other modules

/** Defines:
 */

/* Version:
 *  1.2 (27/09/19) various things and revert to not using C# new syntax to remain compatible
 *  1.1 (25/04/16) aggiunto exception log
 */

namespace Tabula.Log
{
    public class Logger
    {
        private string filename = "";

        private System.IO.StreamWriter 
                                    sw = null, 
                                    sw_ex = null;

        public bool                 TimeOutput = true;
        public bool                 ConsoleOutput = false;
        public bool                 DebugOutput = false;
        public bool                 ExceptionLog = false;           // if true exceptions are created in a special persistent parallel log _exceptions
        public Action<Exception>    defaultLoggerAction = null;
        public Action<string>       onLog = null;

        public static Logger DefaultLog = new Logger("", true);    // default static logger, can be set

        public Logger()
        {
            defaultLoggerAction = new Action<Exception>((ex) => { logException("_default_", ex); });

#if DEBUG
            DebugOutput = true;
#endif
        }

        public Logger(string file, bool purge = true, bool exceptionlog = true)
        {

#if DEBUG
            DebugOutput = true;
#endif

            ExceptionLog = exceptionlog;

            Open(file, purge);
        }

        ~Logger()  // destructor
        {
            Close();
        }

        public bool IsOpen
        {
            get
            {
                return sw != null;
            }
        }

        public void Open(string logfile, bool purge)
        {
            if (string.IsNullOrEmpty(logfile))
                return;

            filename = logfile;

            sw = null;
            try
            {
                if (purge)
                    File.WriteAllText(logfile, "", Encoding.UTF8);

                sw = System.IO.File.AppendText(logfile);
            }
            catch { }

            // Exception log
            if (ExceptionLog)
            {
                string logfile_ex = string.Format("{0}_exceptions.txt", Path.Combine(Path.GetDirectoryName(filename), Path.GetFileNameWithoutExtension(filename)));

                sw_ex = null;
                try
                {
                    sw_ex = System.IO.File.AppendText(logfile_ex);
                }
                catch { }
            }
        }

        public void Close()
        {
            try
            {
                if (sw != null)
                {
                    sw.Flush();
                    sw.Close();
                }
                sw = null;
            }
            catch { }

            try
            {
                if (sw != null)
                {
                    sw_ex.Flush();
                    sw_ex.Close();
                }
                sw_ex = null;
            }
            catch { }
           
        }


        public void WriteLine(string message)
        {
            writeline_sw(message, sw);
        }

        private void writeline_sw(string message, StreamWriter _sw)
        {
            string logLine = "";

            if (TimeOutput)
                logLine = System.String.Format("{0:G} {1}", System.DateTime.Now, message);
            else
                logLine = message;

            if (ConsoleOutput)
            {
                Console.WriteLine(logLine);          
            }

            if (onLog != null)
                onLog(logLine);

            // Avoid double logging to VS debug console
            if (DebugOutput && !ConsoleOutput)
            {
                System.Diagnostics.Debug.WriteLine(logLine);
            }
  

            if (_sw == null)
                return;

            try
            {
                lock (_sw)
                {

                    _sw.WriteLine(logLine);
                    _sw.Flush();
                }
            }
            catch { }
        }

        public void logException(string context, Exception ex)
        {
            string exmsg = String.Format("Exception \"{0}\" ({1}): {2}\nSTACKTRACE:\n{3}", context, ex.ToString(), ex.Message, (ex.StackTrace!=null ? ex.StackTrace.ToString() : ""));

            WriteLine(exmsg);

            if (sw_ex != null)
                writeline_sw(exmsg, sw_ex);
        }


        // added aliases with Log for cmpatibility with Unitu

        public void Log(string message) => WriteLine(message);

        // static version

        public static void PurgeLog(string logfile)
        {
            try
            {
                File.WriteAllText(logfile, "", Encoding.UTF8);
            }
            catch { }
        }

        public static void WriteLine(string logfile, string message)
        {
            System.IO.StreamWriter sw = null;
            try
            {
                sw = System.IO.File.AppendText(logfile);
                string logLine = System.String.Format("{0:G} {1}.", System.DateTime.Now, message);
                sw.WriteLine(logLine);
                sw.Flush();
            }
            catch { }
            finally
            {
                if (sw != null)
                    sw.Close();
            }
        }
    }
}