﻿using kcp2k;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using static Tabula.RPC.Shared.KcpMessage;
using static Tabula.RPC.Shared;

// VERSION 12

namespace Tabula.PMCore.Unity
{
	[StructLayout(LayoutKind.Sequential, Pack = 1)]
	public struct PlayerControllerMessage
	{
		public const int Version = 13;

		public const int KCPMSG_PLAYERCONTROLLER_DATA = 150;		// client -> server
		public const int KCPMSG_PLAYERCONTROLLER_COMMAND = 151;		// server -> client (json payload)
		public const int KCPMSG_PLAYERCONTROLLER_PING = 160;		// client <-> server roundtrip, client initiated

		// Flags
		public const int Flags_GamePad = 1 << 0;
		public const int Flags_Touch = 1 << 1;
		public const int Flags_GyroArcade = 1 << 2;
		public const int Flags_GyroShooter = 1 << 3;
		public const int Flags_GyroDriving = 1 << 4;

		public const int Flags_Gamepad_ButtonNorth = 1 << 10;
		public const int Flags_Gamepad_ButtonSouth = 1 << 11;
		public const int Flags_Gamepad_ButtonEast = 1 << 12;
		public const int Flags_Gamepad_ButtonWest = 1 << 13;
		public const int Flags_Gamepad_ButtonShoulderLeft = 1 << 14;
		public const int Flags_Gamepad_ButtonShoulderRight = 1 << 15;
		public const int Flags_Gamepad_ButtonSelect = 1 << 16;
		public const int Flags_Gamepad_ButtonStart = 1 << 17;

		public const int Flags_Default = Flags_GamePad | Flags_Gamepad_ButtonSouth;

		public byte		version;

		public int		player_id;
		public ulong	seq;

		public uint		flags;

		// Gamepad
		public float	stick1_x, stick1_y;
		public bool		stick1_button;

		public float	stick2_x, stick2_y;
		public bool		stick2_button;

		public int		dpad;

		public bool		button_north;
		public bool		button_south;
		public bool		button_east;
		public bool		button_west;

		public bool		shoulder_left;
		public bool		shoulder_right;

		public float	trigger_left;
		public float	rigger_right;

		public bool		button_select;
		public bool		button_start;

		// touch (max 5 touchpoints)
		public int		touch_count;
		public float	touch1_x;
		public float	touch1_y;
		public float	touch2_x;
		public float	touch2_y;
		public float	touch3_x;
		public float	touch3_y;
		public float	touch4_x;
		public float	touch4_y;
		public float	touch5_x;
		public float	touch5_y;

		// gyro (touch1 position is used)
		public float	heading;
		public float	pitch;
		public float	roll;
		public float	gyro_scale;	// scaling of calculated touch1 position

		#region Send

		public static PlayerControllerMessage DecodeFromPayload(byte[] payload)
		{
			try
			{
				//var msg = new PlayerControllerMessage();

				GCHandle handle = GCHandle.Alloc(payload, GCHandleType.Pinned);
				var msg = (PlayerControllerMessage) Marshal.PtrToStructure(handle.AddrOfPinnedObject(), typeof(PlayerControllerMessage));
				handle.Free();

				return msg;
			}
			catch (Exception ex)
			{
				Log.Logger.DefaultLog.logException("PlayerControllerMessage.Decode()", ex);
				return default;
			}
		}

		// Converts the whole message to a byte array, ready for sending (as payload)
		public byte[] ToByteArray()
		{
			byte[] byteArray = new byte[Marshal.SizeOf(typeof(PlayerControllerMessage))];
			GCHandle handle = GCHandle.Alloc(byteArray, GCHandleType.Pinned);
			Marshal.StructureToPtr(this, handle.AddrOfPinnedObject(), false);
			handle.Free();

			return byteArray;
		}

		public void SendToServer(KcpClient client)
		{
			if (client == null)
				return;

			try
			{
				// tag message with sequence and vesion
				version = Version;
				seq++;

				//  build the payload
				var payload = ToByteArray();

				var m = new KcpMessage()
				{
					header = new _header()
					{
						code = KCPMSG_PLAYERCONTROLLER_DATA,
						msg_id = 0, // not useful?
						chunk_sequence = 1,
						chunk_count = 1,
						payload_chunk_size = (uint)payload.Length,
						payload_total_size = (uint)payload.Length
					},
					payload = payload
				};

				lock (client)
				{
					client?.Send(new ArraySegment<byte>(m.ToByteArray()), KcpChannel.Unreliable);
				}
			}
			catch (Exception ex)
			{
				
			}
		}

		#endregion

		#region Helpers

		public bool GetTouchPoint(int index, out float x, out float y)
		{
			if (index >= touch_count)
			{
				x = 0; y = 0;
				return false;
			}
			else 
				switch(touch_count)
				{					
					case 1: x = touch1_x; y = touch1_y; break;
					case 2: x = touch2_x; y = touch2_y; break;
					case 3: x = touch3_x; y = touch3_y; break;
					case 4: x = touch4_x; y = touch4_y; break;
					case 5: x = touch5_x; y = touch5_y; break;

					case 0:
					default:
						x = 0; y = 0;
						return false;
				}

			return true;
		}

		#endregion
	}


	// Generic server -> client message
	// NOTE: this is sent on game events and also
	[Serializable]
	public class PlayerControllerServerMessage
	{
		// strings commands constants
		public const string SERVER_PLAYER_STATUS = "SERVER_PLAYER_STATUS";
		public const string SERVER_ASK_DISCONNECT = "SERVER_ASK_DISCONNECT";
		public const string SERVER_REPORT_KILL = "SERVER_REPORT_KILL";

		public string	command;

		// Running module info, directly from LocalApp_Scriptable
		public string	package_id;	// the package id (pack)
		public string	module_id;  // the running module id (calibrator, walleroids...)
		public List<string> license_features;

		// Fields are valorized only when needed
		public string	player_name;
		public string	player_avatar;
		public int		player_avatar_index = 0;
		public int		player_color = 0;			// converted from color
		public float	player_health = 0;
		public int		player_lives = 0;

		public float	position_x, position_y;    // viewport space

		public int		player_status;   // same value as PlayerIdentity.Status
		public int		wait_queue = 0;  // position on the queue, 0 = not waiting, 1 = we're next		

		public override bool Equals(object obj)
		{
			switch(obj)
			{
				case PlayerControllerServerMessage m: 
					
					return (player_health == m.player_health && player_status == m.player_status
							&& player_lives == m.player_lives && player_health == m.player_health  &&
							wait_queue == m.wait_queue);

				default:
					return base.Equals(obj);
			}
			
		}
	}
}
