//TABULA_GUID:{4939C3A8-8982-4A18-967B-11F713966AC8}
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace Tabula.Unity
{

	public class ButtonLongPress : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler, IPointerUpHandler, IPointerExitHandler
	{
		[SerializeField]
		[Tooltip("How long must pointer be down on this object to trigger a long press")]
		private float holdTime = 1f;

		public Image progress_filler_image;	// optional image for signaling feedback

		// Remove all comment tags (except this one) to handle the onClick event!
		//private bool held = false;
		//public UnityEvent onClick = new UnityEvent();

		public UnityEvent onLongPress = new UnityEvent();

		private float time_press = -1;

		public void OnPointerDown(PointerEventData eventData)
		{
			Taptic.Selection();

			time_press = Time.time;

			//held = false;
			Invoke("OnLongPress", holdTime);
		}

		public void OnPointerUp(PointerEventData eventData)
		{
			time_press = -1;

			CancelInvoke("OnLongPress");

			//if (!held)
			//    onClick.Invoke();
		}

		public void OnPointerExit(PointerEventData eventData)
		{
			CancelInvoke("OnLongPress");
		}

		private void OnLongPress()
		{
			time_press = -1;

			//held = true;
			onLongPress.Invoke();
		}

		public void Update()
		{
			if (progress_filler_image != null)
			{
				if (time_press != -1)
					progress_filler_image.fillAmount = (Time.time - time_press) / holdTime;
				else
					progress_filler_image.fillAmount = 0;
			}
		}
	}

}