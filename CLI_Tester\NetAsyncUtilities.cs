using System;
using System.Threading;
using System.Threading.Tasks;

namespace Tabula
{
    /// <summary>
    /// .NET equivalent of Unity's CoroutineWaitForTaskResult
    /// Used to track task completion status and handle timeouts
    /// </summary>
    public class AsyncTaskResult
    {
        public bool TaskIsFaulted = false;
        public bool TaskIsCanceled = false;
        public Exception Exception;

        public bool HasTimedOut => Exception != null && Exception is TimeoutException;
    }

    /// <summary>
    /// .NET equivalent of Unity's UnityUtilities for async operations
    /// Provides async/await functionality to replace Unity coroutines
    /// </summary>
    public static class NetAsyncUtilities
    {
        /// <summary>
        /// .NET equivalent of UnityUtilities.WaitForTask() - waits for a task with timeout
        /// </summary>
        /// <param name="task">The task to wait for</param>
        /// <param name="result">Result object to track completion status</param>
        /// <param name="maxTimeSeconds">Maximum time to wait in seconds (-1 for no timeout)</param>
        /// <returns>Task that completes when the original task finishes or times out</returns>
        public static async Task WaitForTaskAsync(Task task, AsyncTaskResult result = null, float maxTimeSeconds = -1)
        {
            var startTime = TimeManager.time;
            
            try
            {
                if (maxTimeSeconds > 0)
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(maxTimeSeconds));
                    await task.WaitAsync(cts.Token);
                }
                else
                {
                    await task;
                }

                if (result != null)
                {
                    result.TaskIsFaulted = task.IsFaulted;
                    result.TaskIsCanceled = task.IsCanceled;
                }
            }
            catch (OperationCanceledException) when (maxTimeSeconds > 0)
            {
                if (result != null)
                {
                    result.Exception = new TimeoutException($"WaitForTask timeout max_time={maxTimeSeconds}");
                }
            }
            catch (Exception ex)
            {
                if (result != null)
                {
                    result.Exception = ex;
                    result.TaskIsFaulted = true;
                }
            }
        }

        /// <summary>
        /// .NET equivalent of UnityUtilities.WaitForTask() for actions - runs action with timeout
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <param name="result">Result object to track completion status</param>
        /// <param name="maxTimeSeconds">Maximum time to wait in seconds (-1 for no timeout)</param>
        /// <returns>Task that completes when the action finishes or times out</returns>
        public static async Task WaitForTaskAsync(Action action, AsyncTaskResult result = null, float maxTimeSeconds = -1)
        {
            var task = Task.Run(action);
            await WaitForTaskAsync(task, result, maxTimeSeconds);
        }

        /// <summary>
        /// .NET equivalent of UnityUtilities.WaitForTask() for functions - runs function with timeout
        /// </summary>
        /// <typeparam name="T">Return type of the function</typeparam>
        /// <param name="func">The function to execute</param>
        /// <param name="result">Result object to track completion status</param>
        /// <param name="maxTimeSeconds">Maximum time to wait in seconds (-1 for no timeout)</param>
        /// <returns>Task that completes when the function finishes or times out</returns>
        public static async Task<T> WaitForTaskAsync<T>(Func<T> func, AsyncTaskResult result = null, float maxTimeSeconds = -1)
        {
            var task = Task.Run(func);
            await WaitForTaskAsync(task, result, maxTimeSeconds);
            return task.IsCompletedSuccessfully ? task.Result : default(T);
        }

        /// <summary>
        /// .NET equivalent of Unity's yield return new WaitForSecondsRealtime()
        /// </summary>
        /// <param name="seconds">Time to wait in seconds</param>
        /// <returns>Task that completes after the specified time</returns>
        public static async Task WaitForSecondsAsync(float seconds)
        {
            if (seconds > 0)
            {
                await Task.Delay(TimeSpan.FromSeconds(seconds));
            }
        }
    }
}
