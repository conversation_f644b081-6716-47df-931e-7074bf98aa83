using System;
using System.Collections.Generic;

namespace Tabula.PWG.MobileController
{
    /// <summary>
    /// .NET equivalent of Unity's StickData struct
    /// </summary>
    [Serializable]
    public struct NetStickData
    {
        public NetVector2 direction;
        public bool is_dirty;

        public void SetData(NetVector2 v)
        {
            is_dirty = true;
            direction.x = v.x;
            direction.y = v.y;
        }
    }

    /// <summary>
    /// .NET equivalent of Unity's ButtonData struct
    /// </summary>
    [Serializable]
    public struct NetButtonData
    {
        public bool pressed;
        public bool is_dirty;

        public void SetData(bool b)
        {
            is_dirty = false;

            if (pressed != b)
            {
                pressed = b;
                is_dirty = true;
            }
        }
    }

    /// <summary>
    /// .NET equivalent of Unity's TriggerData struct
    /// </summary>
    [Serializable]
    public struct NetTriggerData
    {
        public float value;

        public void SetData(float v) => value = v;
    }

    /// <summary>
    /// .NET equivalent of Unity's TouchData struct
    /// </summary>
    [Serializable]
    public struct NetTouchData
    {
        public bool pressed;
        public float x;
        public float y;
        public bool is_dirty;

        public void SetData(bool pressed, float x, float y)
        {
            this.pressed = pressed;
            this.x = x;
            this.y = y;
            is_dirty = true;
        }
    }

    /// <summary>
    /// .NET equivalent of Unity's GyroData struct
    /// </summary>
    [Serializable]
    public struct NetGyroData
    {
        public float heading;
        public float pitch;
        public float roll;

        public void SetData(float heading, float pitch, float roll)
        {
            this.heading = heading;
            this.pitch = pitch;
            this.roll = roll;
        }
    }

    /// <summary>
    /// .NET equivalent of Unity's ControllerData class without ScriptableObject dependency
    /// Provides the same controller input data structure for console applications
    /// </summary>
    [Serializable]
    public class NetControllerData
    {
        public NetStickData[] Sticks;
        public NetButtonData[] Buttons;
        public NetTriggerData[] Triggers;
        public NetTouchData Touch;
        public NetGyroData Gyro;

        public NetControllerData()
        {
            // Initialize with default values matching Unity's ControllerData.asset
            Sticks = new NetStickData[2];
            Buttons = new NetButtonData[11]; // 0-3: NESW, 4-7: unused, 8-9: select/start, 10: extra
            Triggers = new NetTriggerData[2];
            Touch = new NetTouchData();
            Gyro = new NetGyroData();
        }

        public bool IsDirty =>
            Sticks[0].is_dirty ||
            Buttons[0].is_dirty || Buttons[1].is_dirty || Buttons[2].is_dirty || Buttons[3].is_dirty ||
            Buttons[8].is_dirty || Buttons[9].is_dirty || // select,start
            Touch.is_dirty;

        public void ResetDirty()
        {
            Sticks[0].is_dirty = false;

            Buttons[0].is_dirty = false;
            Buttons[1].is_dirty = false;
            Buttons[2].is_dirty = false;
            Buttons[3].is_dirty = false;

            Buttons[8].is_dirty = false;
            Buttons[9].is_dirty = false;

            Touch.is_dirty = false;
        }

        /// <summary>
        /// Creates a sample controller data with stick up input
        /// </summary>
        public static NetControllerData CreateStickUpSample()
        {
            var data = new NetControllerData();
            data.Sticks[0].SetData(new NetVector2(0, 1));
            return data;
        }

        /// <summary>
        /// Creates a sample controller data with stick down input
        /// </summary>
        public static NetControllerData CreateStickDownSample()
        {
            var data = new NetControllerData();
            data.Sticks[0].SetData(new NetVector2(0, -1));
            return data;
        }

        /// <summary>
        /// Creates a sample controller data with stick left input
        /// </summary>
        public static NetControllerData CreateStickLeftSample()
        {
            var data = new NetControllerData();
            data.Sticks[0].SetData(new NetVector2(-1, 0));
            return data;
        }

        /// <summary>
        /// Creates a sample controller data with stick right input
        /// </summary>
        public static NetControllerData CreateStickRightSample()
        {
            var data = new NetControllerData();
            data.Sticks[0].SetData(new NetVector2(1, 0));
            return data;
        }

        /// <summary>
        /// Creates a sample controller data with button pressed
        /// </summary>
        public static NetControllerData CreateButtonSample(int buttonIndex, bool pressed = true)
        {
            var data = new NetControllerData();
            if (buttonIndex >= 0 && buttonIndex < data.Buttons.Length)
            {
                data.Buttons[buttonIndex].SetData(pressed);
            }
            return data;
        }
    }

    /// <summary>
    /// .NET equivalent of Unity's SimulatedData class without ScriptableObject dependency
    /// Provides input simulation data structure for console applications
    /// </summary>
    [Serializable]
    public class NetSimulatedData
    {
        [Serializable]
        public class SimulatedAtom
        {
            public float time;
            public NetControllerData data;

            public SimulatedAtom()
            {
                time = 0f;
                data = new NetControllerData();
            }

            public SimulatedAtom(float time, NetControllerData data)
            {
                this.time = time;
                this.data = data;
            }
        }

        public List<SimulatedAtom> items;

        public NetSimulatedData()
        {
            items = new List<SimulatedAtom>();
        }

        /// <summary>
        /// Creates a simple simulation sequence with basic stick movements
        /// </summary>
        public static NetSimulatedData CreateDefaultSimulation()
        {
            var simData = new NetSimulatedData();
            
            // Add some basic movements with timing
            simData.items.Add(new SimulatedAtom(1.0f, NetControllerData.CreateStickUpSample()));
            simData.items.Add(new SimulatedAtom(1.0f, NetControllerData.CreateStickRightSample()));
            simData.items.Add(new SimulatedAtom(1.0f, NetControllerData.CreateStickDownSample()));
            simData.items.Add(new SimulatedAtom(1.0f, NetControllerData.CreateStickLeftSample()));
            simData.items.Add(new SimulatedAtom(0.5f, NetControllerData.CreateButtonSample(0))); // North button
            simData.items.Add(new SimulatedAtom(0.5f, NetControllerData.CreateButtonSample(1))); // South button

            return simData;
        }
    }
}
