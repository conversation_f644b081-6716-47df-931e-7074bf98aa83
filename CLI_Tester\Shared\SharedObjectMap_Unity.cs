﻿//TABULA_GUID:{19ABE875-3478-4995-934B-759534B849E7}
using System;
using System.Threading.Tasks;
using UnityEngine;

namespace Tabula.SharedObjectMap
{
    public static class Dispatcher
    {
        private static UnityMainThreadDispatcher dispatcher;

        public static void Set(UnityMainThreadDispatcher _dispatcher)
        {
            dispatcher = _dispatcher;
        }

        public static void Dispatch(Action action)
        {
            if (dispatcher != null)
                dispatcher.Enqueue(action);
            else
                action.Invoke();
        }

		public static async Task DispatchAsync(Action action)
		    => Dispatch(action);
	}
}

