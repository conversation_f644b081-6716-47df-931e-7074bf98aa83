using System;
using System.Text;

public static class NobleConnectHelper
{
    public static (string username, string password, string origin)  ParseGameID(string gameId)
    {
        string username = "", password = "", origin = "";
        if (!string.IsNullOrEmpty(gameId))
        {
            string decodedGameID = Encoding.UTF8.GetString(Convert.FromBase64String(gameId));
            string[] parts = decodedGameID.Split('\n');

            if (parts.Length == 3)
            {
                username = parts[1];
                password = parts[2];
                origin = parts[0];
            }
        }
        return (username, password, origin);
    }
    
    public static string RandomString(int length)
    {
        string str = string.Empty;

        do 
        {
            str += Guid.NewGuid().ToString().Replace("-", "");
        }

        while (length > str.Length);

        return str.Substring(0, length);
    }
}