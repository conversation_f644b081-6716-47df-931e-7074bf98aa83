using System;
using Tabula.P2P;

namespace Tabula.PWG.MobileController
{
    /// <summary>
    /// .NET equivalent of Unity's P2P_ServerInfo class
    /// Contains P2P server connection information and status
    /// </summary>
    public class P2P_ServerInfo
    {
        public string ip;
        public int port;

        public P2P.ClientData.State connection_state;
        public AsyncTaskResult cr_result = new AsyncTaskResult();

        public P2P_ServerInfo()
        {
            ip = null;
            port = 0;
            connection_state = P2P.ClientData.State.None;
        }
    }
}
