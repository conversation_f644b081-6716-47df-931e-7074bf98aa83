﻿//TABULA_GUID:{39FD693E-E6B5-4854-8003-358F3CC0098D}
using Newtonsoft.Json;
using System;
using System.IO;
using System.Security.Cryptography;

// New version of Serializer, json based, with encrypt/decrypt

namespace Tabula
{
	public static class Serializer
	{
		// TODO: json settings ?

		public static T DeseralizeFromString<T>(string str)
		{
			return JsonConvert.DeserializeObject<T>(str);
		}

		public static void SerializeToString<T>(T obj, ref string str)
		{
			str = JsonConvert.SerializeObject(obj);
		}

		/* 
         * encryptString
         * provides simple encryption of a string, with a given password
         */
		static public string EncryptString(string clearText, string password)
		{
			byte[] clearBytes = System.Text.Encoding.UTF8.GetBytes(clearText);
			byte[] encBytes = EncryptBytes(clearBytes, password);
			return Convert.ToBase64String(encBytes);
		}

		static public byte[] EncryptBytes(byte[] clearBytes, string password)
		{
			SymmetricAlgorithm algorithm = getSymmetricAlgorithm(password);
			MemoryStream ms = new MemoryStream();
			CryptoStream cs = new CryptoStream(ms, algorithm.CreateEncryptor(), CryptoStreamMode.Write);
			cs.Write(clearBytes, 0, clearBytes.Length);
			cs.FlushFinalBlock();
			cs.Close();
			return ms.ToArray();
		}

		/*
         * decryptString
         * provides simple decryption of a string, with a given password, Base64 always
         */
		static public string DecryptString(string cipherText, string password)
		{
			byte[] cipherBytes = Convert.FromBase64String(cipherText);
			byte[] clearBytes = DecryptBytes(cipherBytes, password);
			return System.Text.Encoding.UTF8.GetString(clearBytes);
		}

		static public byte[] DecryptBytes(byte[] cipherBytes, string password)
		{
			SymmetricAlgorithm algorithm = getSymmetricAlgorithm(password);
			MemoryStream ms = new MemoryStream();
			CryptoStream cs = new CryptoStream(ms, algorithm.CreateDecryptor(), CryptoStreamMode.Write);
			cs.Write(cipherBytes, 0, cipherBytes.Length);
			cs.Close();
			return ms.ToArray();
		}

		static SymmetricAlgorithm _cached_symmetric_algorithm;

		static private SymmetricAlgorithm getSymmetricAlgorithm(string password)
		{
			if (_cached_symmetric_algorithm != null)
				return _cached_symmetric_algorithm;

			SymmetricAlgorithm algorithm = Aes.Create();

			Rfc2898DeriveBytes rdb = new Rfc2898DeriveBytes(
				password,
				new byte[] { 0x43, 0x1f, 0xf4, 0x69, 0x75, 0x67, 0x2b, 0x43, 0x68, 0x3c, 0x6f, 0x12, 0x69, 0x64, 0x65 },
				10,
				HashAlgorithmName.SHA256);

			algorithm.Padding = PaddingMode.ISO10126;
			algorithm.Key = rdb.GetBytes(32);
			algorithm.IV = rdb.GetBytes(16);

			_cached_symmetric_algorithm = algorithm;

			return algorithm;
		}

		// Serializes an object compressing/entcrypting to base64
		public static void SerializeAndEncrypt<T>(T obj, string password, ref string str)
		{
			string json = "";
			SerializeToString<T>(obj, ref json);

			string conf_encrypted_64 = EncryptString(json, password);

			str = conf_encrypted_64;
		}

		// deserializes a compressed/encrypted object from base64
		public static T DeserializeEncryptedObject<T>(string str, string password)
		{
			string conf_uncrypted = DecryptString(str, password);
			T obj = DeseralizeFromString<T>(conf_uncrypted);
			return obj;
		}

		// deserializes a compressed/encrypted file from base64
		public static T DeserializeEncryptedFile<T>(string file, string password)
		{
			if (!File.Exists(file))
				return default(T);

			string str = File.ReadAllText(file);
			return DeserializeEncryptedObject<T>(str, password);
		}

		// Write a string to binary, prepending length and other stuff
		static public byte[] writeToBinary(string str)
		{
			using (MemoryStream ms = new MemoryStream())
			{
				using (BinaryWriter bw = new BinaryWriter(ms))
				{
					bw.Write(str);
					return ms.ToArray();
				}
			}
		}

		// reads back string
		static public string readFromBinary(byte[] bytes, int index, int length)
		{
			using (MemoryStream ms = new MemoryStream(bytes, index, length))
			{
				using (BinaryReader br = new BinaryReader(ms))
				{
					return br.ReadString();
				}
			}
		}
	}
}
