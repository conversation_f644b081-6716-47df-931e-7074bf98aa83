﻿//TABULA_GUID:{6C470C71-36D3-426B-A90E-4175B7F03DE5}
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Tabula.API
{
    // Version 1.1 (07/07/2018) added TABULA_GUID, sync with new "int" based method calls from FaçadeSignage

    /* This is a wrapper generator for a static API class:
     * 
     * The API is a static class, with static attributes marked with an API attribute.
     * The wrapper will build the method cache so they can dynamically called from protocols (rpc, http, osc, ...)
     * The wrapper can also generate a Tabula.RPC.ManagedClient extension class that wires RPC and OSC calls
     * 
     * Usage:
     * 
     * public class MyAPIMethod : Attribute  {}
     * 
     * public static class MyAPI
     * {
     *      [MyAPIMethod]
     *      public static bool myFirstMethod(string name, int parameter)
     *      {}
     * 
     * }
     * 
     * 
     * var wrapper = new APIWrapper(typeof(MyAPI), typeof(MyAPIMethod));
     * 
     * // a call arrives
     * var m = wrapper.getMethod(apicommand);
     * if (m!=null)
     * {
     *      object[] apiargs = (segs.Length > 1 ? APIWrapper.convertArguments(m.args_types, segs, 1) : null);
     *      
     *      // Call methods are virtual and can be reimplemented to allow a queue or other waiting behaviour
     *      m.Call(apiargs);
     * }
     * 
     * 
     * 
     * 
     * 
     */

public class APIWrapper
    {
        public static bool Debug = false;

        private Type ApiClass;
        private Type ApiMethodAttribute;

        public class SkipInContractGenerationAttribute : Attribute
        { }

        public class Batch
        {
            public List<int>        methods = new List<int>();
            public List<object[]>   arguments = new List<object[]>();
        }

        public APIWrapper(Type api_class, Type api_attribute)
        {
            ApiClass = api_class;
            ApiMethodAttribute = api_attribute;

            BuildMethodCache();
        }

        // Virtual call methods, these can be re-implemented to implement a queued behaviour (like in facade)
        // Standard implementation calls it immediately        

        public virtual object Call(int method, IPEndPoint ep,  params object[] args)
        {
            return invoke(method, ep, args);
        }        

        public virtual object[] Call(IPEndPoint ep, Batch batch)
        {
            List<object> results = new List<object>();

            for (int i=0; i < batch.methods.Count; i++)
            {
                results.Add(invoke(batch.methods[i], ep, batch.arguments[i]));
            }

            return results.ToArray();
        }

        // Reflection

        // name based method
        // will return only the FIRST match, so no support for different function prototypes in HTTP/OSC !
        public bool hasMethod(string method_name, ref CachedMethod cm)
        {
            if (MethodCacheByName == null)
                BuildMethodCache();

            if (MethodCacheByName.ContainsKey(method_name))
            {
                cm = MethodCacheByName[method_name];
                return true;
            }
            else
            {
                cm = null;
                return false;
            }
        }

        public bool hasMethod(int method_id)
        {
            if (MethodCache == null)
                BuildMethodCache();

            return MethodCache.ContainsKey(method_id);
        }

        public CachedMethod getMethod(int method_id)
        {
            if (MethodCache == null)
                BuildMethodCache();

            return (MethodCache.ContainsKey(method_id) ? MethodCache[method_id] : null);
        }

        public CachedMethod getMethod(string method_name)
        {
            if (MethodCache == null)
                BuildMethodCache();

            return (MethodCacheByName.ContainsKey(method_name) ? MethodCacheByName[method_name] : null);
        }

        public object invoke(int method_id, IPEndPoint ep)
        {
            if (MethodCache == null)
                BuildMethodCache();

            if (MethodCache.ContainsKey(method_id))
                return MethodCache[method_id].invoke(ep);
            else
                return null;
        }

        public object invoke(int method_id, IPEndPoint ep, params object[] args)
        {
            if (MethodCache == null)
                BuildMethodCache();

            if (MethodCache.ContainsKey(method_id))
                return MethodCache[method_id].invoke(ep,args);
            else
                return null;
        }

        // reflection 

        public class CachedMethod
        {
            public string           method = null;
            public int              method_id = 0;             // generated enum
            public Attribute        attribute = null;       // stores the attribute because it could have other information for call directives

            public Delegate         del=null;               // this is the most generic, will need slower DynamicInvoke
            public Func<object>     func=null;              // function with no args returning an object
            public Action           action=null;            // void method

            public Type[]           args_types = null;      // function signature, if return type is not void the last element is the return type (as for Func signatures)
            public string[]         args_names = null;      // names are useful for automatic contract generation
            public object[]         default_args = null;
            public Type             returntype = null;

            public string           DecoratedMethod
            {
                get
                {
                    string name_decorated = method;
                    if (args_types != null)
                        for (int i = 0; i < args_types.Length - 1; i++)
                            name_decorated += "_" + args_types[i].ToString();

                    return name_decorated;
                }
            }

            // only methods with OSC compatible arguments can be invoked with OSC
            public bool CanUseOSC => true;  // TEST!
             /*
            {
                get
                {
                    if (args_types == null)
                        return true;

                    foreach (var t in args_types)
                        if (!(t.IsPrimitive || t.IsEnum || t == typeof(String) || t == typeof(Single) || t == typeof(Double) || t == typeof(Int32) || t == typeof(Int64) || t == typeof(Boolean) || t == typeof(void)))
                            return false;

                    return true;
                }
            }*/

            public bool NeedJsonSerialization(Type t)
                => !(t.IsPrimitive || t.IsEnum || t == typeof(String) || t == typeof(Single) || t == typeof(Double) || t == typeof(Int32) || t == typeof(Int64) || t == typeof(Boolean) || t == typeof(void));

            public object invoke(IPEndPoint ep, params object[] args)
            {
                if (action != null)
                    action();
                else if (func != null)
                    return func();
                else if (del != null)
                {
                    // if args is not the same count we substitute it with default arguments

                    if (args == null && default_args != null)
                        args = default_args;
                    else if (args != null && default_args != null && args.Length < default_args.Length)
                    {
                        var args_new = args.ToList();
                        for (int a = args.Length; a < default_args.Length; a++)
                            args_new.Add(default_args[a]);

                        args = args_new.ToArray();
                    }

                    // Check for parameter conversion, will lead to much less trouble (ex: Int64 to Int32)
                    // Will also use JSon.net conversion 
                    // Added check for null parameters!
                    for (int i=0; i<args.Length; i++)
                    {
                        // SPECIAL: If the first argument is of type IPEndPoint, then the underlying endpoint is copied from socket info
                        if (i==0 && args_types[0] == typeof(IPEndPoint))
                        {
                            try
                            {
                                args[0] = ep;
                            }
                            catch { }
                        }                        

                        if (args[i] == null)
                            continue;

                        if (args[i].GetType() != args_types[i])
                        {
                            args[i] = changeType(args[i], this.args_types[i]);
                        }
                    }

                    return del.DynamicInvoke(args);
                }

                return null;
            }
            
        }

        // New version is based on the api call index so it is always 1-1 even for 
        public Dictionary<int, CachedMethod> MethodCache = null;

        // Another indexing by <name>, DOES NOT SUPPORT DIFFERENT FUNCTION PROTOTYPES
        public Dictionary<string, CachedMethod> MethodCacheByName = null;

        public void BuildMethodCache()
        {
            MethodCache = new Dictionary<int, CachedMethod>();
            MethodCacheByName = new Dictionary<string, CachedMethod>();

            // Get all api methods
            var methods = ApiClass.GetMethods(BindingFlags.Static | BindingFlags.Public | BindingFlags.FlattenHierarchy).Where(m => m.GetCustomAttributes(ApiMethodAttribute, true).Length > 0).ToArray();

            // will order methods by name so method_id doesn't change always
            int api_method_index = 1;
            foreach (var m in (from om in methods orderby om.Name select om))
            {
                // Try to create faster Func and Action first, it they fail go to delegate
                CachedMethod cm = new CachedMethod();

                cm.method = m.Name;

                // generates a sequential id that will be used to create the enum in generated api
                cm.method_id = api_method_index++;

                // store also attribute that could have additional information 
                cm.attribute = m.GetCustomAttribute(ApiMethodAttribute);

                try
                {
                    cm.action = (Action) Delegate.CreateDelegate(typeof(Action), m);
                }
                catch 
                {
                    try
                    {
                        cm.func = (Func<object>)Delegate.CreateDelegate(typeof(Func<object>), m);
                        cm.returntype = m.ReturnType;
                    }
                    catch
                    {
                        try
                        {
                            cm.args_types = (from parameter in m.GetParameters() select parameter.ParameterType).Concat(new[] { m.ReturnType }).ToArray();
                            cm.args_names = (from parameter in m.GetParameters() select parameter.Name).ToArray();
                            cm.default_args = (from parameter in m.GetParameters() select parameter.DefaultValue).ToArray();
                            cm.del = m.CreateDelegate(Expression.GetDelegateType(cm.args_types));
                            cm.returntype = m.ReturnType;
                        }
                        catch { }
                    }
                }

                if (cm.action != null || cm.func != null || cm.del != null)
                {
                    if (Debug)
                        System.Diagnostics.Debug.WriteLine($"BuildMethodCache: +{m.Name}");

                    MethodCache.Add(cm.method_id, cm);

                    try
                    {
                        MethodCacheByName.Add(cm.method, cm);
                    }
                    catch(Exception ex)
                    {
                        Log.Logger.DefaultLog.logException("BuildMethodCache().MethodCacheByName", ex);
                    }
                }
            }
        }

        // This is a more general Type.changeType() that recognizes special Json.net objects
        public static object changeType(object obj, Type type)
        {
            if (obj is JToken)
                return (obj as JToken).ToObject(type);  // General JSon.Net conversion
            else
                return Convert.ChangeType(obj, type);
        }

		// Will resolve typenames to Type also from other assemblies
        public static Type GetTypeAllAssemblies(string type)
        {
            // Local Assembly first
            var t_local = Type.GetType(type, false);
            if (t_local != null)
                return t_local;

            // All other assemblies (but local one)
            var executing_assembly = Assembly.GetExecutingAssembly();

            foreach (Assembly a in AppDomain.CurrentDomain.GetAssemblies())
            {
                if (a.Equals(executing_assembly))
                    continue;

                var t_external = a.GetType(type, false);
                if (t_external != null)
                    return t_external;
            }

            return null;
        }



        public static string getDefaultValueString(object obj)
        {
            if (obj == null)
                return "null";
            else if (obj is string)
                return $"\"{obj.ToString()}\"";
            else if (obj is bool)
                return obj.ToString().ToLower();
            else
                return obj.ToString();
        }

        // Gets the linted type string and keeps a list of custom classes for later export
        public static string getTypeString(object obj, List<Type> custom_types, bool object_is_typename = false)
        {
            string type_string;

            if (object_is_typename)
                type_string = (string) obj;
            else
            {
                if (obj is Type)
                {
                    Type t = (obj as Type);

                    /*
                    if (t.IsArray)
                        t = t.GetElementType();*/

                    type_string = t.ToString();
                }
                else if (obj is string)
                    type_string = obj as string;
                else if (obj == null)
                    return "void";
                else
                    return "ERROR";
            }

            // If it is a nested type strip the parent class part
            if (type_string.Contains("+"))
                type_string = type_string.Substring(type_string.IndexOf('+') + 1);
            

            // If it is an array let's strip the array part
            bool is_array = type_string.EndsWith("[]");

            if (is_array)
                type_string = type_string.Substring(0, type_string.Length - 2);

            switch (type_string)
            {
                case "System.Void": return "void" + (is_array ? "[]" : "");

                case "System.Object": return "object" + (is_array ? "[]" : "");

                case "System.DateTime": return "System.DateTime" + (is_array ? "[]" : "");

                case "String":
                case "System.String": return "string" + (is_array ? "[]" : "");

                case "Boolean":
                case "System.Boolean": return "bool" + (is_array ? "[]" : "");

                case "System.Int32": return "int" + (is_array ? "[]" : "");
                case "System.Int64": return "long" + (is_array ? "[]" : "");

                case "System.UInt32": return "uint" + (is_array ? "[]" : "");
                case "System.UInt64": return "ulong" + (is_array ? "[]" : "");

                case "Single":
                case "System.Single": return "float" + (is_array ? "[]" : "");

                case "Double":
                case "System.Double": return "double" + (is_array ? "[]" : "");                

                default:

                    // Tuple 2
                    Regex re_list = new Regex(@"^System.Tuple`2\[(.*),(.*)\]");
                    Match m = re_list.Match(type_string);
                    if (m.Success)
                    {
                        string type_string1 = m.Groups[1].Value.Trim();
                        string type_string2 = m.Groups[2].Value.Trim();

                        Type t1 = GetTypeAllAssemblies(type_string1);
                        Type t2 = GetTypeAllAssemblies(type_string2);

                        return string.Format("Tuple<{0},{1}>",
                            (t1 == null ? getTypeString(type_string1, custom_types, true) : getTypeString(t1, custom_types)),
                            (t2 == null ? getTypeString(type_string2, custom_types, true) : getTypeString(t2, custom_types)));
                    }

                    // Tuple 3
                    re_list = new Regex(@"^System.Tuple`3\[(.*),(.*),(.*)\]");
                    m = re_list.Match(type_string);
                    if (m.Success)
                    {
                        string type_string1 = m.Groups[1].Value.Trim();
                        string type_string2 = m.Groups[2].Value.Trim();
                        string type_string3 = m.Groups[3].Value.Trim();

                        Type t1 = GetTypeAllAssemblies(type_string1);
                        Type t2 = GetTypeAllAssemblies(type_string2);
                        Type t3 = GetTypeAllAssemblies(type_string3);

                        return string.Format("Tuple<{0},{1},{2}>",
                            (t1 == null ? getTypeString(type_string1, custom_types, true) : getTypeString(t1, custom_types)),
                            (t2 == null ? getTypeString(type_string2, custom_types, true) : getTypeString(t2, custom_types)),
                            (t3 == null ? getTypeString(type_string3, custom_types, true) : getTypeString(t3, custom_types))
                            );
                    }


                    // Generic List
                    re_list = new Regex(@"^System.Collections.Generic.List`1\[(.*)\]");
                    m = re_list.Match(type_string);
                    if (m.Success)
                    {
                        string type_string1 = m.Groups[1].Value.Trim();
                        Type t = GetTypeAllAssemblies(type_string1);
                        if (t == null)
                            return $"List<{getTypeString(type_string1, custom_types, true)}>";
                        else
                            return $"List<{getTypeString(t, custom_types)}>";
                    }

                    // Tabula.SerializedDictionary`2[System.String, System.String]
                    re_list = new Regex(@"^Tabula.SerializedDictionary`2\[(.*),(.*)\]");
                    m = re_list.Match(type_string);
                    if (m.Success)
                    {
                        string type_string1 = m.Groups[1].Value.Trim();
                        string type_string2 = m.Groups[2].Value.Trim();

                        Type t1 = GetTypeAllAssemblies(type_string1);
                        Type t2 = GetTypeAllAssemblies(type_string2);

                        return string.Format("Tabula.SerializedDictionary<{0},{1}>",
                            (t1 == null ? getTypeString(type_string1, custom_types, true) : getTypeString(t1, custom_types)),
                            (t2 == null ? getTypeString(type_string2, custom_types, true) : getTypeString(t2, custom_types)));
                    }


                    // This is a custom type, need to export it!
                    // Note: handles array types
                    if ((obj is Type))
                    {
                        Type t = (obj as Type);
                        if (t.IsArray)
                            t = t.GetElementType();

                       if (!custom_types.Contains(t))
                            custom_types.Add(t);
                    }

                    return type_string + (is_array ? "[]" : "");
            }
        }

        public static string getClassSource(Type t, List<Type> custom_types)
        {
            // TODO: Exclude some namespaces, this needs to be modularized
            if (t.FullName.StartsWith("Tabula.RPC."))
                return string.Empty;
            if (t.Assembly.FullName.Contains("TabulaCore.Interop"))
                return string.Empty; // old attribute wasn't working anymore

            // Check for specific attribute that excludes this class from contract generation
            if (Attribute.GetCustomAttribute(t, typeof(SkipInContractGenerationAttribute)) != null)
                return string.Empty;

			if (t.IsEnum)
            {
                // Outputs Enum
                var sb = new StringBuilder();
                sb.AppendFormat("[System.Reflection.ObfuscationAttribute(Exclude = true)]\npublic enum {0}\n{{\n", t.Name);

                var enumNames = t.GetEnumNames();
                var enumValues = t.GetEnumValues();
                var enumType = t.GetEnumUnderlyingType();

                List<string> lines = new List<string>();
                for (int i = 0; i < enumNames.Length; i++)                
                    lines.Add($"\t{enumNames[i]} = {Convert.ChangeType(enumValues.GetValue(i), enumType)}");

                sb.AppendLine(string.Join(",\n", lines.ToArray()));
                sb.AppendLine("}");
                return sb.ToString();
            }
            else
            {
                // Outputs class/struct and recurse
                var sb = new StringBuilder();
                sb.AppendLine("[Serializable]");
                sb.AppendLine("[System.Reflection.ObfuscationAttribute(Exclude=true)]");

                string class_or_struct = (t.IsValueType ? "struct" : "class");

                sb.AppendFormat("public {0} {1}\n{{\n", class_or_struct, t.Name);

                // Get nested types and looks for inner-defined enums                
                foreach (var nested_type in t.GetTypeInfo().DeclaredNestedTypes)
                    if (nested_type.IsEnum)
                        sb.AppendLine("\n" + getClassSource(nested_type,null));

                foreach (var field in t.GetFields(BindingFlags.DeclaredOnly | BindingFlags.Instance | BindingFlags.Public))
                {
                    // Skip ignore serialize attributes
                    if (Attribute.IsDefined(field, typeof(XmlIgnoreAttribute)) ||
                        Attribute.IsDefined(field, typeof(JsonIgnoreAttribute)))
                        continue;                    

                    sb.AppendFormat("\tpublic {0}\t{1};\n",
                        //field.FieldType.Name,
                        getTypeString(field.FieldType, custom_types),
                        field.Name);
                }

                foreach (var prop in t.GetProperties())
                {
                    // Skip ignore serialize attributes
                    if (Attribute.IsDefined(prop, typeof(XmlIgnoreAttribute)) ||
                        Attribute.IsDefined(prop, typeof(JsonIgnoreAttribute)))
                        continue;

                    if (prop.CanRead && prop.CanWrite)
                        sb.AppendFormat("\tpublic {0}\t{1}\t{{{2}{3}}}\n",
                            //prop.PropertyType.Name,
                            getTypeString(prop.PropertyType, custom_types),
                            prop.Name,
                            prop.CanRead ? " get;" : "",
                            prop.CanWrite ? " set; " : " ");
                }

                sb.AppendLine("}");
                return sb.ToString();
            }
        }        

        // Converts the arguments according to a signature (taken from methodcache)
        public static object[] convertArguments(Type[] types, object[] segments, int start_index=0)
        {
            List<object> args = new List<object>();

            int idx = 0;
            Type type;
            for (int i = start_index; i < segments.Length; i++)
            {
                //FIXME: what was this for? http?
                //string a = segments[i].Replace("/", "");
                object a = segments[i];

                if (idx >= types.Length)
                {
                    idx++;
                    continue;
                }

                type = types[idx];

                try
                {
                    if (Debug)
                        System.Diagnostics.Debug.WriteLine("convertArguments #{0} Value={1} Type={2}", idx, a, type);

                    // TODO: if problems with nullables http://aspalliance.com/852

                    // Special case int -> Enum
                    if (type.IsEnum)
                        args.Add(Enum.Parse(type, a.ToString(), true));
                    else                        
                        args.Add(ConvertAny(a, type));  // handles also JTokens, was //args.Add(Convert.ChangeType(a, type));
                }
                catch
                {
                    if (Debug)
                        System.Diagnostics.Debug.WriteLine("convertArguments error idx={0}, src={1}, type={2}", idx, a, type);

                    args.Add(null);
                }

                idx++;
            }

            return args.ToArray();
        }

        // Helper to convert based on MethodInfo
        public static object[] convertArguments(MethodInfo minfo, object[] args)
        {
            object[] converted = new object[args.Length];

            int i = 0;
            foreach (var p in minfo.GetParameters())
			{
                converted[i] = ConvertAny(args[i], p.ParameterType);
                i++;
			}

            return converted;
        }

        public static object ConvertAny(object value, Type type)
        {
            if (value == null)
                return null;

            object converted;

            // handles complex objects first, as well as normal primitives
            converted = ConvertFromJson(value, type);

            // other conversion (ex: int64 -> int32)
            if (converted.GetType() != type)
                converted = Convert.ChangeType(value, type);

            return converted;
        }

        public static object ConvertFromJson(object value, Type type)
        {
            if (value is JToken)
                return (value as JToken).ToObject(type);
            else
                return Convert.ChangeType(value, type);
        }

    }
}
