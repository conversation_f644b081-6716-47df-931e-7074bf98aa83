﻿//TABULA_GUID:{D182F859-54FB-4DA9-8083-5F38DA08D1E5}
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Serialization;
using Tabula.Log;
using static Tabula.SharedObjectMap.SharedObjectMap;

namespace Tabula.SharedObjectMap
{
	// Obfuscation attribute (agnostic)
	public class Obfuscation_SkipAttribute : System.Attribute
    { }

    public static partial class SharedObjectMap
    {
        public static GuidObject RootModel { get; private set; }

        public static bool TrackNewGuidObjects = false;

        private static bool _debug = false;
        public static bool Debug
        {
            get => _debug;

            set 
            {
                _debug = value;
                Tabula.RPC.Shared.Debug = value;
            }
        }

        public static SettingsFlags Settings = SharedObjectMap.SettingsFlags.None;

        public static bool IsServer { get; private set; } = false;

        public static Action<List<GuidUpdate>> OnUpdateAdded; // called when updates are added

        // Delayed updates for the model
        //public static Dictionary<long, List<GuidUpdate>> delayed_updates = new Dictionary<long, List<GuidUpdate>>();    // for atomic delayed commit       

        public static Action<GuidUpdate> notifyModelUpdate;   // from_server

        public static Action<GuidObject> notifyObjectAdded;     // new guidobject
        public static Action<GuidObject> notifyObjectRemoved;   // removed guidobject (will no longer be in map when called)

        [Flags]
        public enum SettingsFlags
		{
            None,
            DisableCommit
		}

        // NOTE: debug log is not light.. you've been warned

        public static string log(string str = "")
        {
            Logger.DefaultLog.WriteLine((IsServer ? "SERVER " : "CLIENT ") + str);
            return str;
        }

        public static string log(GuidObject model, string str = "")
        {
            var msg = $"{(IsServer ? "SERVER" : "CLIENT")} {model} | {str}";
            Logger.DefaultLog.WriteLine(msg);
            return msg;
        }

        public static string log(IGuidObjectSyncView view, string str = "")
        {
            var msg = $"{(IsServer ? "SERVER" : "CLIENT")} (VIEW) {view.GetModel()} | {str}";
            Logger.DefaultLog.WriteLine(msg);
            return msg;
        }

        public static string log(GuidUpdate update, string str = "")
        {
            var msg = $"{(IsServer ? "SERVER" : "CLIENT")} {update} | {str}";
            Logger.DefaultLog.WriteLine(msg);
            return msg;
        }

        // Checks if a model is locking an update: it supports guid hierarchy
        public static bool IsCommitDelayed(GuidObject model) => model.IsInCommit;

        public static bool IsCommitDelayed(long guid) => IsCommitDelayed(object_map[guid]);

        public static void CommitBegin_Wrapper(GuidObject model)
		{
            // In order to avoid starvation of pending commits, each begin acts as an end first
            CommitEnd_Wrapper(model);

            model.CommitBegin();
        }

        // Will send all updates as an atomic batch
        public static void CommitEnd_Wrapper(GuidObject model)
        {
            if (Settings.HasFlag(SettingsFlags.DisableCommit))
                return;

            // Invokes the action to commit for this and all guidobjects children
            // Gathers all the accumualated updates
            List<GuidUpdate> commit_updates = new List<GuidUpdate>();

            var action = new Action<GuidObject>((o) =>
            {
                if (/*!o.__is_in_commit || */o.__delayed_updates == null)
                {
                    // can be in commit but without updates, so commit must be always cleared!
                    o.CommitEnd();
                    return;
                }

                lock (o.__delayed_updates)
                {
                    foreach (var u in o.__delayed_updates)
                        commit_updates.Add(u);

                    if (Debug)
                        log($"SERVER: CommitEnd_Wrapper ENQUEUE commit_guid={model.__guid}");
                }

                o.CommitEnd();
            });

            _scanGuidObjects(model,
                new Operation()
                {
                    OperationType = OperationType.CallAction,
                    Action = action,                     
                });

            // process all updates at once, implementation should handle it as a batch
            if (commit_updates.Count>0)
                OnUpdateAdded?.Invoke(commit_updates);
        }

        /*
        public static async Task<bool> CommitEndAndWait(GuidObject model, int timeout = 5000, int delay = 16)
        {
            CommitEnd(model);
            return await waitForCommit(timeout, delay);
        }
        */


        // Will resolve typenames to Type also from other assemblies
        public static Type GetTypeAllAssemblies(string type)
        {
            // Local Assembly first
            var t_local = Type.GetType(type, false);
            if (t_local != null)
                return t_local;

            // All other assemblies (but local one)
            var executing_assembly = Assembly.GetExecutingAssembly();

            foreach (Assembly a in AppDomain.CurrentDomain.GetAssemblies())
            {
                if (a.Equals(executing_assembly))
                    continue;

                var t_external = a.GetType(type, false);
                if (t_external != null)
                    return t_external;
            }

            return null;
        }
        
        public static IEnumerable<Type> GetTypesWithAttributeAllAssemblies(Type attribute)
        {            
            foreach (Assembly a in AppDomain.CurrentDomain.GetAssemblies())
            {                
                foreach (Type type in a.GetTypes())
                {
                    // TODO: filter by namespace ?

                    if (type.GetCustomAttributes(attribute, true).Length > 0)
                    {
                        yield return type;
                    }
                }               
            }
        }

        // will wait until a guid is present and return it
        public static async Task<GuidObject> waitForGuidObject(long guid, int timeout = 5000, int delay=16)
        {
            if (guid == -1)
            {
                Logger.DefaultLog.logException("waitForObject()", new ArgumentException("guid=-1"));
                return null;
            }

            return await Task.Run(async() =>
            {
               GuidObject obj = null;
               var t_end = DateTime.Now.AddMilliseconds(timeout);
               while (true)
               {
                    if (t_end.CompareTo(DateTime.Now) < 0)
                    {
                        // timing out
                        Logger.DefaultLog.logException("waitForObject()", new TimeoutException($"guid={guid}"));
                        return null;
                    }

                    if (TryGetObject(guid, out obj))
                        return obj;

                    await Task.Delay(delay);
               }
            });
        }

        // will wait until a guid is NOT present and return it
        // this is for client to wait on removals of entities
        public static async Task<bool> waitForGuidObjectNotFound(long guid, int timeout = 5000, int delay = 16)
        {
            if (guid == -1)
            {
                Logger.DefaultLog.logException("waitForObjectNotFound()", new ArgumentException("guid=-1"));
                return false;
            }

            return await Task.Run(async () =>
            {
                GuidObject obj = null;
                var t_end = DateTime.Now.AddMilliseconds(timeout);
                while (true)
                {
                    if (t_end.CompareTo(DateTime.Now) < 0)
                    {
                        // timing out
                        Logger.DefaultLog.logException("waitForObjectNotFound()", new TimeoutException($"guid={guid}"));
                        return false;
                    }

                    if (!TryGetObject(guid, out obj))
                        return true;

                    await Task.Delay(delay);
                }
            });
        }

        // Will wait until the delayed updates queue is empty        
        // TODO: reimplement, not delayed updates are local to guidobjects
        /*
        public async static Task<bool> waitForCommit(int timeout = 5000, int delay = 16)
        {
            return await Task.Run(async () =>
            {
                var t_end = DateTime.Now.AddMilliseconds(timeout);
                while (true)
                {
                    if (t_end.CompareTo(DateTime.Now) < 0)
                    {
                        // timing out
                        Logger.DefaultLog.logException("waitForCommit()", new TimeoutException("waitForCommit()"));
                        return false;
                    }

                    lock (delayed_updates)
                        if (delayed_updates.Count == 0)
                            return true;

                    await Task.Delay(delay);
                }
            });
        }
        */


        #region ClientToServer

        // comulative updates, client will call an API method that will call this
        public static SingleUpdateResult[] UpdateFieldBatch(IPEndPoint remote_ep, List<GuidUpdate> updates, bool get_results)
        {
            if (Debug)
                log($"SERVER UpdateFieldBatch BEGIN updates={updates.Count}");

            SingleUpdateResult[] results = (get_results ? new SingleUpdateResult[updates.Count] : null);

            lock(object_map)
                foreach (var u in updates)
                {
                    var ret = UpdateField(remote_ep, u);
                    if (get_results)
                        results[updates.IndexOf(u)] = ret;
                }

            if (Debug)
                log($"SERVER UpdateFieldBatch END");

            return results;
        }

        // Update direct method are the quickest one without any control or return value, optimized for OSC setting of live values
        public static void UpdateFieldDirect<T>(IPEndPoint ep, long guid, string fieldname, T value, int array_index=-1)
        {
            if (!TryGetObject(guid, out GuidObject obj_ref))
                return;

            var obj_field = obj_ref.GetType().GetField(fieldname);

            if (array_index == -1)
            {
                obj_field.SetValue(obj_ref, value);
            }
            else
            {
                // Array element, unchecked
                Array array = obj_field.GetValue(obj_ref) as Array;
                array.SetValue(value, array_index);
            }

            // Updates view also (test performance!)
            if (obj_ref.__view != null)                           
                obj_ref.__view.GetType().GetProperty(fieldname).SetValue(obj_ref.__view, value);            
        }

        private static bool IsPrimitive(object obj) => IsPrimitive(obj.GetType());

        private static bool IsPrimitive(Type t) => t.IsPrimitive || t.IsEnum || t == typeof(String);

        // Helper that will try it's best to find a matching signature
        // In particular: Int64 -> Int32 as Json converts all to long!
        private static MethodInfo GetMethod(object obj_ref, string method_name, Type[] types=null, BindingFlags flags = BindingFlags.Public | BindingFlags.Instance)
        {
            MethodInfo method = null;
            var obj_type = obj_ref.GetType();

            if (types == null)
                return obj_type.GetMethod(method_name, flags);

			// method = obj_type.GetMethod(method_name, flags, types);          // higher framework
			method = obj_type.GetMethod(method_name, flags, null, types, null);
			if (method != null)
                return method;

            // 1st try: converts Int64 to Int32
            for (int i = 0; i < types.Length; i++)
                if (types[i] == typeof(Int64))
                    types[i] = typeof(Int32);

			//method = obj_type.GetMethod(method_name, flags, types);            // higher framework
			method = obj_type.GetMethod(method_name, flags, null, types, null);
			if (method != null)
				return method;

            return null;
		}


        // Will update the model in both server/client mode, if we are on client receiving remote changes we need to trigger also the view refresh!        
        // NOTE: local_update flag is used by views to signal the use of this function just to update back items with received value (so to inhibit extra logging)
        public static SingleUpdateResult UpdateField(IPEndPoint remote_ep, GuidUpdate update, bool local_update = false)
        {
            update.endpoint = remote_ep;

            // IMPORTANT: data can arrive serialized (json), we need to de-serialize the JToken according to the passed type
            if (update.FieldType != null)
                update.FieldValue = ConvertFromJson(update.FieldValue, update.FieldType);

            if (update.FieldName == null && update.UpdateType != UpdateType.CallMethod)
                throw new ArgumentException("UpdateField() cannot receive a GuidUpdate with empty FieldName");

            SingleUpdateResult result = new SingleUpdateResult() { Flags = UpdateResultFlags.None, ChangedValue = null };

            // TODO: lock here is too restrictive? quick calls?
            lock (object_map)
            {
                GuidObject obj_ref = null;
                FieldInfo obj_field = null;

                try
                {
                    if (object_map.ContainsKey(update.Guid))
                    {
                        bool guids_modified = false;

                        obj_ref = object_map[update.Guid] as GuidObject;

                        // Method call!
                        if (update.UpdateType == UpdateType.CallMethod)
                        {
                            MethodInfo method = null;

                            if (update.MethodTypes.Length > 0)
							{
                                // support overloaded methods with different parameters     
                                Type[] args_types = new Type[update.MethodTypes.Length];
                                for (int i = 0; i < args_types.Length; i++)
                                {
                                    // NOTE: since these strings are not fully qualified, some types must be worked out
                                    switch (update.MethodTypes[i])
                                    {
                                        case "System.Net.IPEndPoint":
                                            args_types[i] = typeof(System.Net.IPEndPoint); 
                                            break;

                                        default:
											args_types[i] = Type.GetType(update.MethodTypes[i]);    // strings
                                            break;
									}
                                  
                                }
                                

                                method = GetMethod(obj_ref, update.MethodName, args_types);
                            }
                            else
							{
                                method = GetMethod(obj_ref, update.MethodName);
                            }

                            if (method != null)
                            {
                                // NOTE: also GuidObject's methods follow the rule of endpoint rewriting if first parameter is of type IPEndPoint

                                var method_result = CallMethod(obj_ref, method, remote_ep, update.MethodArguments);

                                result.ChangedValue = method_result;
                                result.Flags |= (UpdateResultFlags.Updated | UpdateResultFlags.ValueChanged);

                                if (Debug && !local_update)
                                    log(update.ToStringMethodCall(obj_ref,method.ReturnType,method_result));
                            }
                            else
                            {
                                result.Flags |= UpdateResultFlags.ErrorGeneric;
                                //TODO: log error
                            }

                            goto validate_and_return;
                        }

                        obj_field = obj_ref.GetType().GetField(update.FieldName);

                        if (obj_field != null)
                        {
                            try
                            {                                                    
                                // Addition/Remove (this can happen server or client side too)
                                // Only IList and IDictionary now
                                if (update.UpdateType == UpdateType.AddItem)    
                                {
                                    if (typeof(IList).IsAssignableFrom(obj_field.FieldType))
                                    {
                                        var ilist = (IList) obj_field.GetValue(obj_ref);

                                        var op = AddAllGuidObjects(update.FieldValue, obj_ref.__guid);
                                        guids_modified = op.GuidsModified;

										object value = null;
                                        if (obj_field.FieldType.IsGenericType)
                                            value = ConvertAny(update.FieldValue, obj_field.FieldType.GetGenericArguments()[0]);
                                        else
                                            value = ConvertAny(update.FieldValue, obj_field.FieldType.GetElementType());

                                        int index = Convert.ToInt32(update.ItemIndexOrKey);
                                        if (index == -1)
                                            ilist.Add(value);   //NOTE: arrays cannot grow
                                        else
                                        {
                                            if (ilist.GetType().IsArray)
                                                ilist[index] = value;   //NOTE: for arrays the insert becomes a set
                                            else
                                                ilist.Insert(index, value);
                                        }

                                        if (IsServer)
                                        {
                                            obj_ref.CommitUpdateItem(obj_field.Name, (int)( index==-1 ? ilist.Count - 1 : index), UpdateType.AddItem);

                                            // invalidate the wrapper
                                            if (obj_ref.__model_wrapper != null)
                                                _invalidateCollection(obj_ref.__model_wrapper, obj_field.Name);
                                        }
                                        else if (obj_ref.__view != null)
                                        {
                                            // update the collection view                              
                                            //obj_ref.__view.GetType().GetProperty(update.FieldName).SetValue(obj_ref.__view, null);
                                            _update_view_collection_add(obj_ref.__view, update.FieldName, value, index);
                                        }

                                        result.Flags |= UpdateResultFlags.Updated;

                                        if (Debug && !local_update)
                                            log(update, $" IList ADD");
                                    }
                                    else if (typeof(IDictionary).IsAssignableFrom(obj_field.FieldType))
                                    {
                                        IDictionary dict = (IDictionary) obj_field.GetValue(obj_ref);

                                        object key = Convert.ChangeType(update.ItemIndexOrKey, obj_field.FieldType.GenericTypeArguments[0]);                                            

                                        if (!dict.Contains(key))
                                        {
                                            var op = AddAllGuidObjects(update.FieldValue, obj_ref.__guid);
                                            guids_modified = op.GuidsModified;

                                            object value = null;
                                            if (obj_field.FieldType.IsGenericType)
                                                value = ConvertAny(update.FieldValue, obj_field.FieldType.GetGenericArguments()[1]);
                                            else
                                                value = ConvertAny(update.FieldValue, obj_field.FieldType.GetElementType());

                                            dict.Add(key, value);

                                            if (IsServer)
                                            {
                                                obj_ref.CommitUpdateItem(obj_field.Name, key, UpdateType.AddItem);

                                                // invalidate the wrapper
                                                if (obj_ref.__model_wrapper != null)
                                                    _invalidateCollection(obj_ref.__model_wrapper, obj_field.Name);
                                            }
                                            else if (obj_ref.__view != null)
                                            {
                                                // update the collection view                              
                                                //obj_ref.__view.GetType().GetProperty(update.FieldName).SetValue(obj_ref.__view, null);
                                                _update_view_collection_add(obj_ref.__view, update.FieldName, value, key);
                                            }                                                                                       

                                            result.Flags |= UpdateResultFlags.Updated;

                                            if (Debug && !local_update)
                                                log(update, $" IDictionary ADD");
                                        }
                                        else
                                        {
                                            // TODO: existing key! revert to setfield?
                                            result.Flags |= UpdateResultFlags.ErrorGeneric;
                                        }                                      
                                    }

                                    if (update.FieldValue is GuidObject)
                                    {
                                        // return added parent guid
                                        result.ChangedValue = (update.FieldValue as GuidObject).__guid;
                                    }

                                    goto validate_and_return;
                                }
                                else if (update.UpdateType == UpdateType.RemoveItem) 
                                {
                                    if (typeof(IList).IsAssignableFrom(obj_field.FieldType))
                                    {
                                        var ilist = (IList)obj_field.GetValue(obj_ref);

                                        int index = Convert.ToInt32(update.ItemIndexOrKey);

                                        var op = RemoveAllGuidObjects(ilist[index]);
                                        guids_modified = op.GuidsModified;

                                        if (ilist.GetType().IsArray)
                                            ilist[index] = null;
                                        else
                                            ilist.RemoveAt(index);

                                        if (IsServer)
                                        {
                                            obj_ref.CommitUpdateItem(obj_field.Name, index, UpdateType.RemoveItem);

                                            // invalidate the wrapper
                                            if (obj_ref.__model_wrapper != null)
                                                _invalidateCollection(obj_ref.__model_wrapper, obj_field.Name);
                                        }
                                        else if (obj_ref.__view != null)
                                        {
                                            // update the collection view                              
                                            //obj_ref.__view.GetType().GetProperty(update.FieldName).SetValue(obj_ref.__view, null);
                                            _update_view_collection_remove(obj_ref.__view, update.FieldName, index);
                                        }
                                      
                                        result.Flags |= UpdateResultFlags.Updated;

                                        if (Debug && !local_update)
                                            log(update, $" IList REMOVE");
                                    }
                                    else if (typeof(IDictionary).IsAssignableFrom(obj_field.FieldType))
                                    {
                                        IDictionary dict = (IDictionary)obj_field.GetValue(obj_ref);

                                        object key = Convert.ChangeType(update.ItemIndexOrKey, obj_field.FieldType.GenericTypeArguments[0]);

                                        if (dict.Contains(key))
                                        {
                                            var op = RemoveAllGuidObjects(dict[key]);
                                            guids_modified = op.GuidsModified;

                                            dict.Remove(key);

                                            if (IsServer)
                                            {
                                                obj_ref.CommitUpdateItem(obj_field.Name, key, UpdateType.RemoveItem);

                                                // invalidate the wrapper
                                                if (obj_ref.__model_wrapper != null)
                                                    _invalidateCollection(obj_ref.__model_wrapper, obj_field.Name);
                                            }
                                            else if (obj_ref.__view != null)
                                            {
                                                // update the collection view                              
                                                //obj_ref.__view.GetType().GetProperty(update.FieldName).SetValue(obj_ref.__view, null);
                                                _update_view_collection_remove(obj_ref.__view, update.FieldName, key);
                                            }

                                            result.Flags |= UpdateResultFlags.Updated;

                                            if (Debug && !local_update)
                                                log(update, $" IDictionary REMOVE");
                                        }
                                        else
                                        {
                                            // TODO: no such key
                                            result.Flags |=  UpdateResultFlags.ErrorGeneric;
                                        }                                                                              

                                       
                                    }

                                    goto validate_and_return;
                                }
								else if (update.UpdateType == UpdateType.ClearItems)
								{
									if (typeof(IList).IsAssignableFrom(obj_field.FieldType))
									{
										var ilist = (IList)obj_field.GetValue(obj_ref);
										
										var op = RemoveAllGuidObjects(ilist);
										guids_modified = op.GuidsModified;

                                        if (ilist.GetType().IsArray)
                                            ilist = null;
                                        else
                                            ilist.Clear();

										if (IsServer)
										{
                                            obj_ref.CommitUpdate(obj_field.Name);

											// invalidate the wrapper
											if (obj_ref.__model_wrapper != null)
												_invalidateCollection(obj_ref.__model_wrapper, obj_field.Name);
										}
										else if (obj_ref.__view != null)
										{
											// update the collection view                              
                                            _invalidateCollection(obj_ref.__view, update.FieldName); // ?
										}

										result.Flags |= UpdateResultFlags.Updated;

										if (Debug && !local_update)
											log(update, $" IList CLEAR");
									}
									else if (typeof(IDictionary).IsAssignableFrom(obj_field.FieldType))
									{
										IDictionary dict = (IDictionary)obj_field.GetValue(obj_ref);

										var op = RemoveAllGuidObjects(dict);
										guids_modified = op.GuidsModified;

                                        dict.Clear();

										if (IsServer)
										{
											obj_ref.CommitUpdate(obj_field.Name);

											// invalidate the wrapper
											if (obj_ref.__model_wrapper != null)
												_invalidateCollection(obj_ref.__model_wrapper, obj_field.Name);
										}
										else if (obj_ref.__view != null)
										{
											// update the collection view                              
											_invalidateCollection(obj_ref.__view, update.FieldName);
										}

										result.Flags |= UpdateResultFlags.Updated;

										if (Debug && !local_update)
											log(update, $" IDictionary CLEAR");
									}

									goto validate_and_return;
								}



								// Set the value on the model field
								update.PreviousValue = obj_field.GetValue(obj_ref);

                                // Setting IList or Array item by index
                                if (update.ItemIndexOrKey != null)
                                {
                                    if (obj_field.FieldType.IsArray)
                                    {
                                        Array array = update.PreviousValue as Array;
                                        int idx = Convert.ToInt32(update.ItemIndexOrKey);

                                        update.PreviousValue = array.GetValue(idx);

                                        array.SetValue(ConvertAny(update.FieldValue, array.GetType().GetElementType()), idx);

                                        result.Flags |= UpdateResultFlags.Updated;

                                        if (Debug && !local_update)
                                            log(update, $" Array");
                                    }
                                    else if (typeof(IList).IsAssignableFrom(obj_field.FieldType))
                                    {
                                        int list_idx = Convert.ToInt32(update.ItemIndexOrKey);

                                        IList list = update.PreviousValue as IList;
                                        update.PreviousValue = list[list_idx];
                                        list[list_idx] = ConvertAny(update.FieldValue, list.GetType().GetGenericArguments()[0]);    // TEST

                                        result.Flags |= UpdateResultFlags.Updated;

                                        if (Debug && !local_update)
                                            log(update, $" IList");
                                    }
                                    else if (typeof(IDictionary).IsAssignableFrom(obj_field.FieldType))
                                    {
                                        //FIXME: this is a bit rough..
                                        IDictionary dict = update.PreviousValue as IDictionary;
                                        try
                                        {
                                            //TODO: check for key existence and add it?
                                            //TODO: not AddAllGuidObjects() as a standard way?

                                            update.PreviousValue = dict[update.ItemIndexOrKey];
                                            dict[update.ItemIndexOrKey] = ConvertAny(update.FieldValue, dict.GetType().GetGenericArguments()[1]);   // TEST
                                        }
                                        catch(ArgumentException)
                                        {
                                            // ?
                                            var key = Convert.ChangeType(update.FieldValue, dict[update.ItemIndexOrKey].GetType()); // still useful?
                                            update.PreviousValue = dict[key];
                                            dict[update.ItemIndexOrKey] = key;
                                        }
                                        catch(Exception ex)
                                        {
                                            Logger.DefaultLog.logException(log(update, "EXCEPTION"), ex);
                                        }

                                        result.Flags |= UpdateResultFlags.Updated;

                                        if (Debug && !local_update)
                                            log(update, $" IDictionary");
                                    }
                                    else
                                    {

                                        result.Flags |= UpdateResultFlags.FieldNotFound;
                                        throw new Exception("ERROR FieldNotFound");
                                    }

                                    // Use helper method to raise the view notify just the keyed item
                                    if (obj_ref.__view != null)
                                    {
                                        // if it is a collection, invalidate the view so it is rebuilt (OVERKILL!)
                                        // _invalidateCollection(obj_ref.__view, update.FieldName);

                                        //TODO: mimic setting property value directly? or not useful here?
                                        _updateViewCollectionItem(update.IsFromServer, obj_ref.__view, update.FieldName, update.ItemIndexOrKey);
                                    }

                                    goto validate_and_return;
                                }

                                if (update.PreviousValue != null && !IsPrimitive(obj_field.FieldType))
                                {
                                    // replacing a non primitive value (container or Guidobject derived) means that guids from that branch should be removed and re-added
                                    var op = RemoveAllGuidObjects(update.PreviousValue);
                                    guids_modified = op.GuidsModified;
                                }

                                // Directly convert update value in-place, so we get it also in notifications
                                if (update.FieldValue == null || update.FieldValue.GetType().Equals(obj_field.FieldType))
                                { 
                                    // already converted
                                }
                                else
                                    update.FieldValue = ConvertAny(update.FieldValue, obj_field.FieldType);  //Convert.ChangeType(update.FieldValue, obj_field.FieldType);


                                // MODEL: Set the model field value
                                obj_field.SetValue(obj_ref, update.FieldValue);

                                if (Debug && !local_update)
                                    log(update);

                                // VIEW: Set also the view property value, this will ensure a proper set will call all the DependsOn etc.. 
                                // Since model and view will have the same value if we are on the client, an equality check will prevent the Sync
                                if (obj_ref.__view != null)
                                {
                                    // if it is a collection or a class, invalidate the view so it is rebuilt
                                    bool just_refresh = !IsPrimitive(update.FieldValue) || _invalidateCollection(obj_ref.__view, update.FieldName);

                                    // Set the view, but use null value for collections or classes, because in that case set is just used to refresh (this is the default logic for generated views)
                                    obj_ref.__view.GetType().GetProperty(update.FieldName).SetValue(obj_ref.__view, (just_refresh ? null : update.FieldValue));

                                    //NOTE: the notify will happen in SetValue() above inside the generated view, but since it is impossibile to tell if
                                    //      it's from server, we need an extra notify here to distinguish
                                    obj_ref.__view.NotifyViewUpdate(true, obj_ref.__view, update.FieldName);
								}

                                if (update.FieldValue != null && !IsPrimitive(obj_field.FieldType))
                                {
                                    // replacing a non primitive value (container or Guidobject derived) means that guids from that branch should be removed and re-added
                                    var op = AddAllGuidObjects(update.FieldValue);
                                    guids_modified = op.GuidsModified;
                                }

                                // keep the sequenceid in pseudo-sync for debugging purposes
                                SetUpdateSequence(update.Seq);

                                if (guids_modified)
                                {
                                    result.ChangedValue = update.FieldValue;
                                    result.Flags |= (UpdateResultFlags.Updated | UpdateResultFlags.ValueChanged);
                                }
                                else
                                    result.Flags |= UpdateResultFlags.Updated;
                            }
                            catch
                            {
                                result.Flags |= UpdateResultFlags.ErrorSettingValue;
                                throw new Exception("ERROR ErrorSettingValue");
                            }
                        }
                        else
                        {
                            result.Flags |= UpdateResultFlags.FieldNotFound;
                            throw new Exception("ERROR FieldNotFound");
                        }
                    }
                    else
                    {
                        result.Flags |= UpdateResultFlags.ObjectNotFound;
                        throw new Exception("ERROR ObjectNotFound");
                    }
                }
                catch (Exception ex)
                {
                    Logger.DefaultLog.logException(log(update, $"EXCEPTION {ex.ToString()} {ex.Message}"), ex);
                }

validate_and_return:

                // Notify the model update, it is the class responsibility to commit back update fields if validation occurs
                if (IsServer)
                    obj_ref?.NotifyModelUpdate(update);

                return result;
            }
        }

        public static bool GetField(long guid, string fieldName, out FieldInfo finfo, out GuidObject guidobject)
        {
            lock (object_map)
            {
                try
                {
                    if (object_map.ContainsKey(guid))
                    {
                        guidobject = object_map[guid] as GuidObject;
                        finfo = guidobject.GetType().GetField(fieldName);
                        return true;
                    }
                    else
                    {
                        finfo = null;
                        guidobject = null;
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    Logger.DefaultLog.logException(log("GetField EXCEPTION"), ex);
                    throw ex;
                }
            }
        }

        public static object GetFieldValue(long guid, string fieldName, int item_index = -1, object item_key = null)
        {
            lock (object_map)
            {
                try
                {
                    if (object_map.ContainsKey(guid))
                    {
                        GuidObject obj_ref = object_map[guid] as GuidObject;
                        FieldInfo f = obj_ref.GetType().GetField(fieldName);
                        if (f != null)
                        {
                            try
                            {
                                // Getting IList or Array item by index
                                if (item_index != -1)
                                {
                                    if (f.FieldType.IsArray)
                                    {
                                        Array array = f.GetValue(obj_ref) as Array;
                                        return array.GetValue(item_index);
                                    }
                                    else if (typeof(IList).IsAssignableFrom(f.FieldType))
                                    {
                                        IList list = f.GetValue(obj_ref) as IList;
                                        return list[item_index];
                                    }

                                    throw new Exception(log($"GetFieldValue: guid={guid} FieldName={fieldName} item_index={item_index} cannot get field value!"));
                                }

                                // Getting a dictionary item value by key
                                if (item_key != null && typeof(IDictionary).IsAssignableFrom(f.FieldType))
                                {
                                    IDictionary dict = f.GetValue(obj_ref) as IDictionary;
                                    return dict[item_key];
                                }

                                return f.GetValue(obj_ref);
                            }
                            catch
                            {
                                throw new Exception(log($"GetFieldValue: guid={guid} cannot get field value"));
                            }
                        }
                        else
                        {
                            throw new Exception(log($"GetFieldValue: guid={guid} FieldName={fieldName} not found!"));
                        }
                    }
                    else
                    {
                        throw new Exception(log($"GetFieldValue: guid={guid} not found!"));
                    }
                }
                catch (Exception ex)
                {
                    Logger.DefaultLog.logException(log("GetFieldValue EXCEPTION"), ex);
                    throw ex;
                }
            }
        }
        
        // Server perchè le View devono notificare il cambiamento
        // FIXME: cache invalidation?? now disabled
        // private static Dictionary<string, Tuple<MethodInfo,Type>> _update_item_cache = new Dictionary<string, Tuple<MethodInfo,Type>>();
        private static void _updateViewCollectionItem(bool from_server, object view, string fieldName, object item_index_or_key)
        {
            // caching
            // if (!_update_item_cache.TryGetValue(fieldName, out Tuple<MethodInfo, Type> update_helper))
            //{
                MethodInfo m = GetMethod(view, $"_{fieldName}_update_item");
                ParameterInfo p = m.GetParameters()[1];

                var update_helper = new Tuple<MethodInfo, Type>(m, p.ParameterType);
                //_update_item_cache.Add(fieldName, update_helper);
            //}
            

            // change-type is for safe operation of index types (ex: in64 vs int32)
            update_helper?.Item1.Invoke(view, new object[] { from_server, Convert.ChangeType(item_index_or_key,update_helper.Item2) });
        }

        // If called with empty parameter, will invalidate ALL collections
        // Return true if the fieldName passed is really a collection that has been invalidated
        // NOTE: this method is identical for views and wrappers
        public static bool _invalidateCollection(object view_or_wrapper, string fieldName)
        {
            MethodInfo m = GetMethod(view_or_wrapper, $"_invalidateCollection", flags: BindingFlags.Instance | BindingFlags.NonPublic);
            if (m!=null)
                return (bool) m.Invoke(view_or_wrapper, new object[] { fieldName });

            return false;
        }
        
        // TODO: cache ?
        public static void _update_view_collection_add(object view_or_wrapper, string fieldName, object new_item, object index_or_key)
        {
            MethodInfo m = GetMethod(view_or_wrapper, $"_{fieldName}_viewcoll_add", flags: BindingFlags.Instance | BindingFlags.NonPublic);

            // NOTE: two signatures can be found, with only the index/key (value type) or both (guidobject)
            if (m != null)
			{
                if (m.GetParameters().Length == 1)
                    m.Invoke(view_or_wrapper, new object[] { index_or_key });
                else
                    m.Invoke(view_or_wrapper, new object[] { index_or_key, new_item });
            }
        }

        public static void _update_view_collection_remove(object view_or_wrapper, string fieldName, object index_or_key)
        {
            MethodInfo m = GetMethod(view_or_wrapper, $"_{fieldName}_viewcoll_remove", flags: BindingFlags.Instance | BindingFlags.NonPublic);
            m?.Invoke(view_or_wrapper, new object[] { index_or_key });
        }

#endregion


        #region Helpers

        public static List<T> GetObjectsOfClass<T>() where T : GuidObject
        {
            List<T> objs = new List<T>();

            foreach (var kvp in SharedObjectMap.object_map)
            {
                if (typeof(T).IsAssignableFrom(kvp.Value.GetType()))
                {
                    objs.Add((T)kvp.Value);
                }
            }

            return objs;
        }

        // TEST: method to simulate a random change in any of the GuidObjects, using wrappers
        private static Random _rand = new Random();
        private static int _random_last_key = 0;
        public static void RandomGuidObjectChange()
        {
            //var sel_key = object_map.Keys.ElementAt(_rand.Next(1, object_map.Count()));
            var sel_key = object_map.Keys.ElementAt(_random_last_key);
            GuidObject obj = object_map[sel_key] as GuidObject;

            _random_last_key = (_random_last_key + 1) % object_map.Count;

            // Scan Fields 
            FieldInfo[] fields = obj.GetType().GetFields(BindingFlags.Public | BindingFlags.Instance);            

            while (true)
            {
                FieldInfo f = fields.ElementAt(_rand.Next(1, fields.Length));

                // skip special fields
                if (_is_field_special(f.Name))
                    continue;

                Type member_type = f.FieldType;

                // we do not consider other GuidObjects
                if (typeof(GuidObject).IsAssignableFrom(member_type))
                    continue;

                object member_value = f.GetValue(obj);       

                if (IsPrimitive(member_type))
                {
                    obj.CommitUpdate(f.Name);
                }
                else if (member_type.IsGenericType)
                {
                    // IList
                    if (typeof(IList).IsAssignableFrom(member_type))
                    {
                        var item_type = f.FieldType.GenericTypeArguments[0];
                        if (typeof(GuidObject).IsAssignableFrom(item_type))
                            continue;

                        var w = ((IList)member_value)[0];
                        obj.CommitUpdateItem(f.Name, 0);

                        foreach (var i in (IList)member_value)
                        { }
                    }

                       

                    // IDictionary
                    if (typeof(IDictionary).IsAssignableFrom(member_type))
                        foreach (DictionaryEntry i in (IDictionary) member_value)
                        { }

                }
                else if (member_type.IsArray)
                {
                    foreach (var i in (IEnumerable)member_value)
                    { }
                }

                // just one field at a time
                break;
            }

        }

        #endregion

        #region ServerToClient

		// public static List<GuidUpdate> ServerUpdates = new List<GuidUpdate>();

		// this sequence always increments, even if new client disconnect/connect
		// it is the client responsibility to sync with the last sequene
		//public static long ServerUpdateSequence { get; set; } = 1L;

		public static void AddUpdate(GuidUpdate update, bool force_update=false)  // force update will bypass commit status, used internally
        {
            GuidObject model = object_map[update.Guid];
            if (!force_update && model.IsInCommit)
            {
                model.AddDelayedUpdate(update);             
            }
            else
            {
                if (Debug)
                    log(update, "AddUpdate");

                // handler to manage updates directly (ex: sending back to client)
                OnUpdateAdded?.Invoke(new List<GuidUpdate>() { update });
            }
        }

        // NOTE: Deprecated, was used when updates where polled now SharedObjectMap_ClientServer's implementation is event based, there should be no need
        // this is called from the API
        // if -1 is passed all pending updates will be returned
        // will optionally filter updates based on string path/sub-paths
        /*
        public static ServerUpdateResult GetUpdates(IPEndPoint remote_ep=null, long client_update_sequence = -1, HashSet<string> selective_updates=null)
        {
            var update = new ServerUpdateResult() { UpdateSequence = client_update_sequence };

            lock (ServerUpdates)
            {
                if (client_update_sequence!=1 && client_update_sequence >= ServerUpdateSequence)
                    return update;

                //NOTE: These updates could countain guids of objects no more existing, so the client could not find them
                update.UpdateSequence = ServerUpdateSequence;

                if (selective_updates != null)
                {
                    var filtered_updates = new List<GuidUpdate>();

                    foreach (var u in ServerUpdates)
                    {
                        foreach (var f in selective_updates)
                            if (u.Path.StartsWith(f))
                                filtered_updates.Add(u);
                    }

                    update.Updates = filtered_updates.ToArray();
                }
                else
                {
                    update.Updates = ServerUpdates.ToArray();
                }
                
            }

            return update;
        }
        */

        /*
        public static void ClearServerUpdates()
        {
            lock (ServerUpdates)
            {
                ServerUpdates.Clear();
            }
        }
        */

        #endregion





        #region MapOperations

        public enum OperationType
        {
            InitializeServer,   // will regenerate guids
            InitializeClient,   // will add existing guids, not generate, and clear views
            Add,                // will also generate guid if not valorized
            Remove,
            Replace,            // Add and Remove in one shot
            FindPath,           // walks until guid is found, building a path
            CallAction,         // calls an action for each GuidObject child
            CleanGuids,         // cleans the guids,
            CleanPathCache      // clean all path caches
        }

        public class FieldPath
        {
            public string           FieldName;  // if empty this is the index part of the previous collection field
            public CollectionType   Collection;
            public object           CollectionIndex;
            

            public enum CollectionType
            {
                Field, Array, List, Dictionary                
            }

            public FieldPath(string fieldname, CollectionType collection_type = CollectionType.Field, object collection_index=null)
            {
                FieldName = fieldname;
                Collection = collection_type;
                CollectionIndex = null;
            }
        }

        public class Operation
        {
            public OperationType OperationType;

            // Params
            public long Guid = -1;
            public Action<GuidObject> Action;

            // Results
            public List<FieldPath> Path;        // the composed path as a concatenated structure, ready to be turned in a string
            public List<GuidObject> GuidObjects = new List<GuidObject>(); // the guidobjects added/removed
            public bool GuidsModified => GuidObjects.Count()>0;  // true if guids have been removed or added   

            public bool Finished = false;       // true if the Op has produced what is needed

            public string GetPathAsAstring()
            {
                if (Path == null)
                    return string.Empty;

                StringBuilder sb = new StringBuilder();                
                for (int i=0; i<Path.Count; i++)
                {
                    FieldPath p = Path[i];

                    switch (p.Collection)
                    {
                        case FieldPath.CollectionType.Field:
                            sb.Append(p.FieldName);                            
                            break;
                        
                        case FieldPath.CollectionType.Array:
                        case FieldPath.CollectionType.List:
                        case FieldPath.CollectionType.Dictionary:
                            sb.Append($"{p.FieldName}[{p.CollectionIndex}]");
                            break;
                    }

                    if ((i + 1 != Path.Count))
                        sb.Append(".");
                }

                return sb.ToString();
            }
        }
        

        public static Dictionary<long, GuidObject> object_map = new Dictionary<long, GuidObject>();
        private static long GuidSequence = 0L;       // Guids are long integers
        private static long GuidUpdateSequence = 0L; // Sequence for GuidUpdate objects generated both by server and client (independently)

        // Used to keep in pseudo-sync the sequence between client and servers
        public static void SetUpdateSequence(long seq)
        {
            //lock (object_map)
                GuidUpdateSequence = seq;
        }

        public static long GetNextUpdateSequence() => Interlocked.Increment(ref GuidUpdateSequence);
        /*
        {
            // Caused deadlocks in Unity ???
            //lock (object_map)
            {
                

                return ++GuidUpdateSequence;
            }
        }*/

        public static long NextGuid => Interlocked.Increment(ref GuidSequence);
        /*
        {
            get
            {
                lock (object_map)
                {
                    return ++GuidSequence;
                }
            }
        }
        */

        public static GuidObject GetObject(long guid)
        {
            //lock (object_map)
            {
                return object_map[guid];
            }
        }

        public static bool TryGetObject(long guid, out GuidObject obj)
        {
            //lock (object_map)
            {
                return object_map.TryGetValue(guid, out obj);
            }
        }

        public static bool TryGetObject<T>(long guid, out T obj) where T : GuidObject
        {
            //lock (object_map)
            {
                if (object_map.TryGetValue(guid, out GuidObject _obj))
                {
                    obj = (T)_obj;
                    return true;
                }
                else
                {
                    obj = null;
                    return false;
                }
            }
        }



        // Initialize a model: root must be a GuidObject
        // SERVER: re-generates any guid, and start tracking addition/deletions
        // CLIENT: no operation is done, guids must already be there 
        private static void _initializeModel(GuidObject root, bool is_server)
        {
            RootModel = root;

            object_map.Clear();
            GuidSequence = 0;

            IsServer = is_server;

            if (IsServer)            
                _scanGuidObjects(RootModel, new Operation() { OperationType = OperationType.InitializeServer }, -1);    
            else
                _scanGuidObjects(RootModel, new Operation() { OperationType = OperationType.InitializeClient }, -1);
            
            // after scan GuidSequence is updated to the next usable guid

            TrackNewGuidObjects = IsServer;
        }

        public static void InitializeModelAsServer(GuidObject root)
        {
            _initializeModel(root, true);

            foreach (var o in object_map.Values)
                notifyObjectAdded?.Invoke(o);
        }

        public static void InitializeModelAsClient(GuidObject root)
        {
            _initializeModel(root, false);

            foreach (var o in object_map.Values)
                notifyObjectAdded?.Invoke(o);
        }

        // Clears the current model destroying every single object
        // optionally starts from another root object
        public static void ClearModel(GuidObject root = null)
        {
            GuidObject _root = (root != null ? root : RootModel);

            if (_root == null)
                return;

            RemoveAllGuidObjects(_root);            

            RootModel = null;
            RootWrapper = null;

            object_map.Clear();
        }

        public static W GetRootWrapper<W>() where W : IWrapper, new()
        {
            if (RootWrapper == null || !(RootModel.Equals(RootWrapper.GetModel())))
            {
                RootWrapper = new W();
                RootWrapper.SetModel(RootModel);
            }

            return (W) RootWrapper;
        }

        // Will use reflection to "scan" an object for any GuidObject found, recursively and perform various actions Add/Remove to the map
        // NOTE: This is usually the first and only action after loading a model so new guid tracking is started after
        // NOTE: this must be applicable also to non GuidObjects like lists / dictionaries
        public static Operation AddAllGuidObjects(object instance, long guid_parent = -1L, StringBuilder log = null, int depth = 0)
        {
            if (instance == null)
                return new Operation();

            lock (instance)
            {
                var op = new Operation() { OperationType = OperationType.Add };
                long parent_guid = guid_parent != -1L ? guid_parent : ((instance is GuidObject) ? (instance as GuidObject).__guid_parent : -1L);
                _scanGuidObjects(instance, op, parent_guid, log, depth);

                // invoke add method here
                foreach (var o in op.GuidObjects)
                    notifyObjectAdded?.Invoke(o);

                return op;
            }            
        }

        // Removes all nested GuidObjects refereces, as well as the wrappers
        public static Operation RemoveAllGuidObjects(object instance, StringBuilder log = null, int depth = 0)
        {
            if (instance == null)
                return new Operation();

            lock (instance)
            {
                var op = new Operation() { OperationType = OperationType.Remove };
                long parent_guid = ((instance is GuidObject) ? (instance as GuidObject).__guid_parent : -1L);
                _scanGuidObjects(instance, op, parent_guid, log, depth);

                if (op.GuidObjects.Count > 0)
                {
                    // Something has been removed, invalidate all model path caches for safety
                    InvalidatePathCaches(RootModel);
                }

                // invoke remove method here
                foreach (var o in op.GuidObjects)
                    notifyObjectRemoved?.Invoke(o);

                return op;
            }
        }

        // Invalidates all guidobjects __path_cache in a hierarchy, used after a deletion
        public static Operation InvalidatePathCaches(object instance)
        {
			if (instance == null)
				return new Operation();

			lock (instance)
			{
				var op = new Operation() { OperationType = OperationType.CleanPathCache };
				long parent_guid = ((instance is GuidObject) ? (instance as GuidObject).__guid_parent : -1L);
				_scanGuidObjects(instance, op, parent_guid);

				return op;
			}
		}

        public static string FindGuidObjectPathAsString(long guid_to_find, object root=null)
        {
            if (root == null)
                root = RootModel;

            lock (root)
            {
                var op = new Operation() { OperationType = OperationType.FindPath, Guid = guid_to_find };
                _scanGuidObjects(root, op, -1, null, 0, null, new List<FieldPath>());
                return op.GetPathAsAstring();
            }
        }

        public static List<FieldPath> FindGuidObjectPath(long guid_to_find, object root = null)
        {
            if (root == null)
                root = RootModel;

            lock (root)
            {
                var op = new Operation() { OperationType = OperationType.FindPath, Guid = guid_to_find };
                _scanGuidObjects(root, op, -1, null, 0, null, new List<FieldPath>());
                return op.Path;
            }
        }

        public static void GetParentExtended(GuidObject obj, out GuidObject parent, out string parent_field, out object index_or_key)
        {
            parent = obj.__Parent;
            if (parent == null)
            {
                parent_field = "";
                index_or_key = null;
                return;
            }

            parent_field = obj.GetParentFieldName();

            if (typeof(IList).IsAssignableFrom(parent.GetType()))
            {
                IList list = parent as IList;
                index_or_key = list.IndexOf(obj);
            }
            else if (typeof(IDictionary).IsAssignableFrom(parent.GetType()))
            {
                // TODO: Array
                index_or_key = null;
            }
            else
            {
                // TODO: Array
                index_or_key = null;
            }

        }

        // checks if a fieldname is a special one
        private static bool _is_field_special(string fieldname)
        {
            return (fieldname == "__guid" || fieldname == "__guid_parent" ||  fieldname == "__view" || fieldname == "__view_parent" || fieldname == "__view_container" || fieldname == "__model_wrapper");
        }

        public static void _scanGuidObjects(object instance, Operation op, long guid_parent=-1, StringBuilder log = null, int depth = 0, FieldInfo field = null, List<FieldPath> field_path=null)
        {
            if (instance == null)
                return;

            var instance_type = instance.GetType();

            // 1) check if this is a GuidObject
            if (instance is GuidObject guidobject)
            {
                switch (op.OperationType)
                {
                    case OperationType.CleanGuids:
                        guidobject.__guid = -1L;
                        guidobject.__guid_parent = -1L;
                        break;

					case OperationType.CleanPathCache:
						guidobject.__path_cache = null;
						break;

					// Used to re-generate all guidobjects, so to discard the ones coming from deserialization (SERVER initilization)
					case OperationType.InitializeServer:
                        guidobject.__guid = NextGuid;
                        guidobject.__guid_parent = guid_parent;
                                                           
                        if (Add(guidobject.__guid, guidobject))
                            op.GuidObjects.Add(guidobject);

                        break;

                    // Used to add server-received guids, and clear collections so they will be recreated (CLIENT initilization)
                    case OperationType.InitializeClient:
                        if (Add(guidobject.__guid, guidobject))
                            op.GuidObjects.Add(guidobject);

                        if (guidobject.__view != null)
                        {
                            _invalidateCollection(guidobject.__view, null);
                        }
                        break;

                    // Used to add all existing guids to map
                    // NOTE: behaviour is different from Client and Server
                    // Client: should NEVER generate new guids, it it happens it is an error!
                    // Server: should ALWAYS generate new guids 
                    case OperationType.Add:
                        if (IsServer)
						{
                            guidobject.__guid = NextGuid;
                            guidobject.__guid_parent = guid_parent;
                        }
                        else
						{
                            // client must always received valid guids from server
                            if (guidobject.__guid == -1L)
                                throw new Exception($"Client GuidObject has no guid!");
                        }

                        if (Add(guidobject.__guid, guidobject))
                            op.GuidObjects.Add(guidobject);

                        break;

                    // Will also remove all wrappers related to this instance
                    case OperationType.Remove:
                        if (Remove(guidobject.__guid))
                            op.GuidObjects.Add(guidobject);

                        //TEST: Not losing the guid for logs
                        //guidobject.__guid = -1L;
                        guidobject.__model_wrapper = null;  
                        
                        guidobject.__view = null;
                        guidobject.__view_parent = null;
                        guidobject.__view_container = null;                            
                        
                        break;

                    // NOTE: special operation used only in CloneGuidObject, must be tested (probably working only in Server)
                    case OperationType.Replace:
                        if (Remove(guidobject.__guid))
                            op.GuidObjects.Add(guidobject);

                        guidobject.__model_wrapper = null;

                        // clear all views
                        guidobject.__view = null;
                        guidobject.__view_parent = null;
                        guidobject.__view_container = null;                        

                        // generate the new guid
                        guidobject.__guid = NextGuid;
                        guidobject.__guid_parent = guid_parent;                                             

                        if (Add(guidobject.__guid, guidobject))
                            op.GuidObjects.Add(guidobject);

                        break;

                    case OperationType.FindPath:
                        if (guidobject.__guid == op.Guid)
                        {
                            op.Path = field_path;
                            op.Finished = true;
                            return;
                        }
                        break;

                    case OperationType.CallAction:
                        op.Action?.Invoke(guidobject);
                        break;
                }

                log?.Append($"{new String('\t', depth)} {field?.Name} ({instance.GetType().Name}) guid={guidobject.__guid}\n");

                // 2) Scan Fields only! NOTE: Properties are dangerous.. !!!
                FieldInfo[] fields = instance.GetType().GetFields(BindingFlags.Public | BindingFlags.Instance);

                // Looking for: Objects, Arrays, IList, IDictionary
                foreach (var f in fields)
                {
                    // skip XmlIgnore attributes
                   if (Attribute.IsDefined(f, typeof(XmlIgnoreAttribute))) 
                        continue;

                    // skip special fields
                    if (_is_field_special(f.Name))
                        continue;

                    if (IsPrimitive(f.FieldType))
                        continue;

                    object value = f.GetValue(instance);

                    /*
                    if (Debug && op.OperationType!=OperationType.FindPath)
                        SharedObjectMap.log($"_scan {op.OperationType}, GuidObject({guidobject.__guid}).Field={f.Name}, depth={depth}");
                    */

                    List<FieldPath> child_field_path = null;
                    if (field_path != null)
                    {
                        child_field_path = new List<FieldPath>(field_path);
                        child_field_path.Add(new FieldPath(f.Name));
                    }
                        
                    //$"{field_path}{(string.IsNullOrEmpty(field_path) ? "" : ".")}{f.Name}"
                    _scanGuidObjects(value, op, guidobject.__guid, log, depth + 1, f, child_field_path);
                    if (op.Finished)
                        return;
                }

            }
            else if (instance_type.IsGenericType)
            {
                // IList
                if (typeof(IList).IsAssignableFrom(instance_type))
                {
                    int idx = 0;
                    foreach (var i in (IList)instance)
                    {
                        /*
                        if (Debug && op.OperationType != OperationType.FindPath)
                            SharedObjectMap.log($"_scan {op.OperationType}, IList[{idx}] depth={depth}");
                            */

                        List<FieldPath> child_field_path = null;
                        if (field_path != null)
                        {
                            child_field_path = new List<FieldPath>(field_path);

                            var p = child_field_path[child_field_path.Count - 1];
                            p.Collection = FieldPath.CollectionType.List;
                            p.CollectionIndex = idx;
                        }
                        // $"{field_path}[{idx}]"

                        _scanGuidObjects(i, op, guid_parent, log, depth, null, child_field_path);
                        if (op.Finished)
                            return;
                        idx++;
                    }
                }

                // IDictionary
                if (typeof(IDictionary).IsAssignableFrom(instance_type))
                    foreach (DictionaryEntry i in (IDictionary)instance)
                    {
                        /*
                        if (Debug && op.OperationType != OperationType.FindPath)
                            SharedObjectMap.log($"_scan {op.OperationType}, IDictionary.Key[{i.Key}] depth={depth}");

                        _scanGuidObjects(i.Key, op, guid_parent, log, depth);    // ? really useful?
                        if (op.Finished)
                            return;
                       

                        if (Debug && op.OperationType != OperationType.FindPath)
                            SharedObjectMap.log($"_scan {op.OperationType}, IDictionary.Value[{i.Value}] depth={depth}");
                         */

                        List<FieldPath> child_field_path = null;
                        if (field_path != null)
                        {
                            child_field_path = new List<FieldPath>(field_path);

                            var p = child_field_path[child_field_path.Count - 1];
                            p.Collection = FieldPath.CollectionType.Dictionary;
                            p.CollectionIndex = i.Key;
                        }
                        //$"{field_path}[{i.Key}]"

                        _scanGuidObjects(i.Value, op, guid_parent, log, depth, null, child_field_path);
                        if (op.Finished)
                            return;
                    }
            }
            else if (instance_type.IsArray)
            {
                int idx = 0;
                foreach (var i in (IEnumerable)instance)
                {
                    /*
                    if (Debug && op.OperationType != OperationType.FindPath)
                        SharedObjectMap.log($"_scan {op.OperationType}, Array[{idx}] depth={depth}");
                    */

                    List<FieldPath> child_field_path = null;
                    if (field_path != null)
                    {
                        child_field_path = new List<FieldPath>(field_path);

                        var p = child_field_path[child_field_path.Count - 1];
                        p.Collection = FieldPath.CollectionType.Array;
                        p.CollectionIndex = idx;
                    }
                    // $"{field_path}[{idx}]"

                    _scanGuidObjects(i, op, guid_parent, log, depth, null, child_field_path);
                    if (op.Finished)
                        return;

                    idx++;
                }
            }            
        }

        // Cache of GuidObject serializable fields, indexed by type
        public static Dictionary<Type, List<FieldInfo>> guidobject_fields = new Dictionary<Type, List<FieldInfo>>();
        public static List<FieldInfo> GetSerializedFields(GuidObject obj)
        {
            if (!guidobject_fields.TryGetValue(obj.GetType(), out List<FieldInfo> serialized_fields))
            {
                serialized_fields = new List<FieldInfo>();
                FieldInfo[] fields = obj.GetType().GetFields(BindingFlags.Public | BindingFlags.Instance);

                foreach (var f in fields)
                {
                    // skip XmlIgnore attributes
                    if (Attribute.IsDefined(f, typeof(XmlIgnoreAttribute)))
                        continue;

                    // skip special fields
                    if (_is_field_special(f.Name))
                        continue;

                    serialized_fields.Add(f);
                }

                guidobject_fields.Add(obj.GetType(), serialized_fields);
            }

            return serialized_fields;
        }

        public enum CopyFieldsOperation
        {
            Default = 0,
            Include = 1, // only consider fields in list
            Exclude = 2  // exclude fields in list
        }

        // The Json cloning should supersede XmlSerializeClone(), faster and is the the basic step for a real GuidObject clone     
        public static T CloneWithJson<T>(T source)
        {
            if (source == null)
                return default;

            // Primitive types are just returned
            if (IsPrimitive(source))
                return source;

            var serialized = JsonConvert.SerializeObject(source);
            return JsonConvert.DeserializeObject<T>(serialized);
        }

        public static T CloneGuidObject<T>(T source) where T : GuidObject, new()
        {
            // this will actually create a temporary guid immediately removed during copy (FIXME: true?)
            T dest = CloneWithJson(source);

            // Clean the Guids on the destination hierarchy
            _scanGuidObjects(dest, new Operation() { OperationType = OperationType.CleanGuids });

            return dest;
        }

        // Helper to copy all/some fields of the GuidObject to a dest one
        // NOTE: this will ONLY copy flat hierarchies of fields, skipping GuidObjects and collections
        public static void CopyGuidObjectFieldsTo(GuidObject source, GuidObject dest, bool commit_updates, CopyFieldsOperation operation = CopyFieldsOperation.Default, List<string> field_list=null, List<string> changed_fields=null)
        {
            var fields = GetSerializedFields(source);

            List<FieldInfo> to_commit = new List<FieldInfo>();

            foreach (var f in fields)
            {
                // TODO: FIXME: check field can be copied, is not a guidobject or a collection of guidobjects

                switch (operation)
                {
                    case CopyFieldsOperation.Include:
                        if (!field_list.Contains(f.Name))
                            continue;
                        break;

                    case CopyFieldsOperation.Exclude:
                        if (field_list.Contains(f.Name))
                            continue;
                        break;
                }

                object this_value = f.GetValue(source);
                object other_value = f.GetValue(dest);
                
                // never use == since it is an identity test! (same ref)
                if (!object.Equals(this_value,other_value))
                {
                    // update it
                    f.SetValue(dest, this_value);

                    // commit if requested
                    if (commit_updates)
                        to_commit.Add(f);                        

                    if (changed_fields != null)
                        changed_fields.Add(f.Name);
                }                
            }           

            // Commit if requested
            foreach (var f in to_commit)
                dest.CommitUpdate(f);
        }

        public static bool Add(long guid, GuidObject obj_ref)
        {
            if (guid == -1L)
                return false;

            lock (object_map)
            {
                if (!object_map.ContainsKey(guid))
                {
                    object_map.Add(guid, obj_ref);

                    if (Debug)
                        log(obj_ref, "AddedToMap");

                    return true;
                }
                else
                {
                    if (Debug)
                        log(obj_ref, "AlreadyInMap");

                    // TODO: throw exception?
                    return false;
                }
            }
        }

        public static bool Remove(long guid)
        {
            lock (object_map)
            {
                if (object_map.ContainsKey(guid))
                {
                    if (Debug)
                        log(object_map[guid], "RemovedFromMap");

                    object_map.Remove(guid);

                    return true;
                }
                else
                {
                    if (Debug)
                        log($"Cannot remove from map, guid={guid}");

                    return false;
                }
            }
        }

        #endregion

        #region Wrappers

        // the RootWrapper is used to build the property paths when getting views directly from the model
        public static IWrapper RootWrapper { get; set; }

        public static W getWrapperFromModel<M, W>(M model) where W : WrapperBase<M>, new() where M : GuidObject
        {
            if (model.__model_wrapper != null)
                return (W)model.__model_wrapper;

            if (RootWrapper == null)
                throw new ArgumentException(log("No RootWrapper defined"));

            // Wrapper has not been created yet, so query the model path and then evaluate the wrapper property path
            // this will ensure the full hierarchy of wrappers with parent is created an assigned
            string model_path = FindGuidObjectPathAsString(model.__guid);
            W wrapper = (W) GetWrapperFromPath(RootWrapper, model_path);

            if (wrapper == null)
            {
                // problem!
                throw new ArgumentException(log($"Wrapper not found! Path={model_path}"));
            }

            return wrapper;            
        }


        // uses reflection to evaluate a path of fields from a root object, example: Model.List1[3].Prop1.Prop2
        // TODO: quick hack need to test!
        public static object GetModelFromPath(GuidObject root, string propertypath)
        {
            var path = propertypath.Split('.');

            object current_obj = root;
            try
            {
                foreach (var field in path)
                {
                    string name = field;
                    string index = string.Empty;

                    // isolate name and indexer
                    Regex re = new Regex(@"(.*)\[(.*)\]");
                    var match = re.Match(field);
                    if (match.Success)
                    {
                        name = match.Groups[1].Value;
                        index = match.Groups[2].Value;
                    }

                    var f_info = current_obj.GetType().GetField(name, BindingFlags.Public | BindingFlags.Instance);
                    current_obj = f_info.GetValue(current_obj);

                    if (!string.IsNullOrEmpty(index))
                    {
                        // https://stackoverflow.com/questions/937224/propertyinfo-getvalue-how-do-you-index-into-a-generic-parameter-using-reflec
                        String indexerName = ((DefaultMemberAttribute)current_obj.GetType().GetCustomAttributes(typeof(DefaultMemberAttribute), true)[0]).MemberName;
                        PropertyInfo pi2 = current_obj.GetType().GetProperty(indexerName);

                        // index can be int or dict key!
                        try
                        {
                            current_obj = pi2.GetValue(current_obj, new object[] { Convert.ToInt32(index) });
                        }
                        catch (ArgumentException)
                        {
                            current_obj = pi2.GetValue(current_obj, new object[] { index });
                        }
                        catch (FormatException)
                        {
                            current_obj = pi2.GetValue(current_obj, new object[] { index });
                        }
                        catch (Exception ex)
                        {
                            Logger.DefaultLog.logException(log($"GetWrapperFromPath() property_path={propertypath}"), ex);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Logger.DefaultLog.logException($"GetModelFromPath({propertypath})", ex);
                throw new ArgumentException($"Exception getting view from path={propertypath} current_obj={current_obj} type={current_obj.GetType()}");
            }

            return current_obj;
        }



        // uses reflection to evaluate a path of properties from a root object, example: Model.List1[3].Prop1.Prop2
        public static object GetWrapperFromPath(IWrapper root, string propertypath)
        {
            var path = propertypath.Split('.');

            object current_obj = root;
            try
            {
                foreach (var property in path)
                {
                    string name = property;
                    string index = string.Empty;

                    // isolate name and indexer
                    Regex re = new Regex(@"(.*)\[(.*)\]");
                    var match = re.Match(property);
                    if (match.Success)
                    {
                        name = match.Groups[1].Value;
                        index = match.Groups[2].Value;
                    }

                    var p_info = current_obj.GetType().GetProperty(name, BindingFlags.Public | BindingFlags.Instance);
                    current_obj = p_info.GetValue(current_obj);

                    if (!string.IsNullOrEmpty(index))
                    {
                        // https://stackoverflow.com/questions/937224/propertyinfo-getvalue-how-do-you-index-into-a-generic-parameter-using-reflec
                        String indexerName = ((DefaultMemberAttribute)current_obj.GetType().GetCustomAttributes(typeof(DefaultMemberAttribute), true)[0]).MemberName;
                        PropertyInfo pi2 = current_obj.GetType().GetProperty(indexerName);

                        // Make sure the wrapper collection is in sync (as wrappers do sync only if used directly)
                        IWrapperCollection iwc = (IWrapperCollection) current_obj;
                        iwc?.Rebuild();

                        // index can be int or dict key!
                        try
                        {
                            current_obj = pi2.GetValue(current_obj, new object[] { Convert.ToInt32(index) });
                        }
                        catch(ArgumentException)
                        {
                            current_obj = pi2.GetValue(current_obj, new object[] { index });
                        }
                        catch(FormatException)
                        {
                            current_obj = pi2.GetValue(current_obj, new object[] { index });
                        }
                        catch(Exception ex)
                        {
                            Logger.DefaultLog.logException(log($"GetWrapperFromPath() property_path={propertypath}"), ex);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Logger.DefaultLog.logException($"GetWrapperFromPath({propertypath})", ex);
                throw new ArgumentException($"Exception getting view from path={propertypath} current_obj={current_obj} type={current_obj.GetType()}");
            }

            return current_obj;
        }

        #endregion

        // View metadata used for the class generation
        private class ModelDefinition
		{
            public string Class;        // the class name
            public string BaseClass;    // the base class

            public bool IsValueView;    // true for non guid-object derived

            public ModelStructure Structure = new ModelStructure();
		}

        // View metadata used to generate views
        private class ModelStructure
        {
            public List<Tuple<string, Type>> Fields = new List<Tuple<string, Type>>();
            public List<Tuple<string, string>> Guidobjects = new List<Tuple<string, string>>();                     // these will also become views / wrappers

            public List<Tuple<string, string, Type, Type>> ILists = new List<Tuple<string, string, Type, Type>>();              // fieldname, viewclass,  fieldtype, elementtype
            public List<Tuple<string, Type, string>> Guidobjects_ILists = new List<Tuple<string, Type, string>>();  // fieldname, fieldtype, elementtype_view

            public List<Tuple<string, string, Type, Type>> IDictionaries = new List<Tuple<string, string, Type, Type>>();               // fieldname, viewclass, key_type, value_type
            public List<Tuple<string, Type, string>> Guidobjects_IDictionaries = new List<Tuple<string, Type, string>>();   // fieldname, key_type, value_type_view

            public List<MethodInfo> CallableMethods = new List<MethodInfo>();

            public List<Tuple<string, Type>> Ignored = new List<Tuple<string, Type>>();
        }

        // VIEW GENERATION

        // Instantiates views at runtime, allowing for polymorphic types
        public static IGuidObjectSyncView CreateView(object model, Tabula.SharedObjectMap.IGuidObjectSyncView parent = null, object container = null)
        {                  
            Type view_type = GetTypeAllAssemblies($"{model.GetType().FullName}View");    // will throw if view class does not exist for this model

            if (view_type == null)
                throw new ArgumentException($"No view for model type: {model.GetType().FullName}");

            IGuidObjectSyncView  view = (IGuidObjectSyncView) Activator.CreateInstance(view_type, new object[] { model, parent, container });

            return view;
        }

        public static void GenerateViewsFromInstance(Type instance_type, string NameSpace, string ModelNameSpace, string output_file)
        {
            var output = new StringBuilder();

            //var view_map = new Dictionary<string, ModelStructure>();
            var view_map = new List<ModelDefinition>();

            lock (view_map)
            {
                // will only add "used" classes in the model
                _GenerateViewsFromInstance(instance_type, view_map);

                // Make sure classes flagged with GuidObjectClassAttribute are always there
                var types_to_include = GetTypesWithAttributeAllAssemblies(typeof(GuidObjectClassAttribute)).ToList();
                foreach (var t in types_to_include)
                    _GenerateViewsFromInstance(t, view_map);
            }

            // scan view_map and generate code
            output.Append($@"
using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Diagnostics;
using PropertyChanged;
using Tabula.SharedObjectMap;
using {ModelNameSpace};

// Views auto-generated on: {DateTime.Now.ToString()}

namespace {NameSpace}
{{");

            // Generate ViewsExtensions extension methods helpers
            output.Append($@"
    public static class ViewsExtensions
    {{");

            foreach (var v in view_map)
            {
                var modelname = v.Class;
                var view = v.Structure;               

                // Value views do not generate extensions
                if (v.IsValueView)				
                    continue;

                output.Append($@"
        public static {modelname}View getView(this {modelname} m) => ({modelname}View) Tabula.SharedObjectMap.SharedObjectMap.getViewFromModel(m);");
            }

            output.Append($@"
    }}
");

            string root_reference = "";

            foreach (var v in view_map)
            {
                var modelname = v.Class;

                // Value views are just inheriting for extending view with new properties
                if (v.IsValueView)
                {
                    output.Append($@"

    [AddINotifyPropertyChangedInterface]
    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class {modelname}View : {v.BaseClass}
    {{
        public {modelname}View(long parent_guid, string collection_fieldname, object item_index) : base(parent_guid, collection_fieldname, item_index)
        {{}}
    }}
");
                    continue;
                }

                var view = v.Structure;

                bool is_root = modelname == instance_type.Name;

                string root_instance = "";
                if (is_root)
                {
                    root_reference = $"public {modelname}View Root => {modelname}View.Instance;";

                    root_instance = $@"public static {modelname}View Instance;

        public {modelname}View()
        {{
            Instance = this;
        }}
";
                }
                else
                {
                    root_instance = $@"public {modelname}View()
        {{}}
";
                }


                // Initially get (GuidObject) view values in order to initialize them (otherwise UpdateField() from server would not trigger
                var initialize_views = "";

                foreach (var f in view.Guidobjects)
                {
                    var fieldname = f.Item1;
                    initialize_views += $"\t\t\tvar {fieldname}_value = {fieldname};\n";
                }

				// GuidObjectSyncView<{modelname}> View
				#region GuidObjectSyncView<{modelname}> View

				output.Append($@"
    [AddINotifyPropertyChangedInterface]
    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class {modelname}View : {v.BaseClass}
    {{
        {root_instance}
        {(!is_root ? root_reference : "")}

        public static implicit operator {modelname}View({modelname} m)
        {{
            var v = new {modelname}View(m);
            return v;
        }}

        public {modelname}View({modelname} model, Tabula.SharedObjectMap.IGuidObjectSyncView parent=null, object container=null)
        {{
            SetModel(model, parent, container);

            // Initialize views
{initialize_views}

            OnCreate();
        }}

        // helper to invalidate a collection
        private bool _invalidateCollection(string name)
        {{
            switch(name)
            {{
");
                var collections = new List<string>();
                foreach (var f in view.ILists)
                    collections.Add(f.Item1);
                foreach (var f in view.IDictionaries)
                    collections.Add(f.Item1);
                foreach (var f in view.Guidobjects_ILists)
                    collections.Add(f.Item1);
                foreach (var f in view.Guidobjects_IDictionaries)
                    collections.Add(f.Item1);

                // single invalidate
                foreach (var f in collections)                
                    output.AppendLine($@"               case nameof({f}): _{f}=null; return true;");

                // invalidate all
                output.AppendLine($@"               case null:");
                foreach (var f in collections)
                    output.AppendLine($@"                   _{f}=null;");

                output.Append($@"                   return true;
            }}

            return false;
        }}
");
                #endregion

				// Primitive fields
				#region Primitive fields

				foreach (var f in view.Fields)
                {
                    var fieldname = f.Item1;
                    var fieldtype = (f.Item2 as Type).FullName.Replace("+", ".");

                    output.Append($@"
        [DoNotCheckEquality]
        public {fieldtype} {fieldname}
        {{
            get => Model.{fieldname};

            set
            {{");

                    var ftype = Type.GetType($"{NameSpace}.{modelname}");
                    if (ftype != null)
                    {

                        bool directsync = Attribute.IsDefined(Type.GetType($"{NameSpace}.{modelname}").GetField(fieldname), typeof(GuidObjectFieldAttribute));

                        /*
                        // DEPRECATED: instead of defining special methods, let's flag the update with the quick sync hint
                        if (directsync)
                        {
                            output.Append($@"
                    // DIRECT SYNC
                Model.{fieldname} = value;
                Tabula.SharedObjectMap.SharedObjectMap.SyncDirect(Model.__guid, nameof({fieldname}), value);");
                        }
                        else
                        */
                        {   // NEW VERSION: primitive fields do not need waiting for result, if value change in validate server will commit
                            // UPDATE: let's always set, but sync only if value is different, so we can use Set on view when updateField() is called, this will honour DependsOn 
                            // UPDATE: introduce validation with overridable method
                            output.Append($@"                
                
                var validated_value = ({fieldtype}) GetValidatedValue(value); 

                if (!object.Equals(Model.{fieldname}, validated_value))
                    {{
                        const bool directsync = {directsync.ToString().ToLower()};
                        var update = Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), null, validated_value);
                        if (directsync) update.Flags |= Tabula.SharedObjectMap.UpdateFlags.DirectSync;

                        Tabula.SharedObjectMap.SharedObjectMap.Sync( update, false); 
                    }}

                Model.{fieldname} = validated_value;
                RaisePropertyChanged(nameof({fieldname}));
                
                NotifyViewUpdate(false, this, nameof({fieldname}));");
                        }
                    }

                    output.Append($@"               
            }}
        }}
");

                }

				#endregion

				// Guidobjects fields
				#region Guidobjects fields

				// Set with a null will just refresh
				foreach (var f in view.Guidobjects)
                {
                    var fieldname = f.Item1;
                    var fieldtype_view = f.Item2.Replace("+", ".");

                    output.Append($@"
        [DoNotCheckEquality]
        public {fieldtype_view} {fieldname}
        {{
            get
            {{
                if (Model.{fieldname} == null)
                    return null;    // null/nullable types

                if (Model.{fieldname}.__view == null)
                {{
                    var v = new {fieldtype_view}(Model.{fieldname}, Model.__view);
                }}

                return ({fieldtype_view}) Model.{fieldname}.__view;
            }}

            set
            {{
                if (value != null)
                {{
                    var oldmodel = Model.{fieldname};
                    Model.{fieldname} = value.Model;
                    if (oldmodel != null)
                        Model.{fieldname}.__guid_parent = oldmodel.__guid_parent;
                    Model.{fieldname}.__view = null;
                    {fieldname}.Update();
                }}

                NotifyViewUpdate(false, this, nameof({fieldname}));               
                RaisePropertyChanged(nameof({fieldname}));
            }}
        }}

");
                }

				#endregion

				// IList / Array of primitives
				#region IList / Array of primitives

				foreach (var f in view.ILists)
                {
                    var fieldname = f.Item1;               
                    var viewclass = f.Item2+"View";
                    var fieldtype = f.Item3;
                    var elementtype = (f.Item4 as Type).FullName.Replace("+", ".");
                    string collection_length = (fieldtype.IsArray ? "Length" : "Count");
                    string nullable_questionmark = ((f.Item4 as Type).IsValueType ? "?" : "");

                    // Add/remove helpers not for Arrays
                    if (!fieldtype.IsArray)
                        output.Append($@"

        public void {fieldname}_Add({elementtype} item, int index = -1)
        {{
            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), typeof({elementtype}), item, index,  Tabula.SharedObjectMap.UpdateType.AddItem), false);
        }}

        public async Task {fieldname}_AddAsync({elementtype} item, int index = -1, int timeout=5000)
        {{
            var size = {fieldname}.Count;

            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), typeof({elementtype}), item, index,  Tabula.SharedObjectMap.UpdateType.AddItem), false);

			var sw = new Stopwatch();
			while (sw.ElapsedMilliseconds < timeout)
			{{
				if ({fieldname}.Count != size)
                    return;

				await Task.Delay(10);
			}}

			throw new TimeoutException(""{fieldname}_AddAsync() timeout"");
        }}

        public void {fieldname}_Remove(int index)
        {{
            // call a special sync that will add a new object server-side
            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), null, null, index, Tabula.SharedObjectMap.UpdateType.RemoveItem), false);
        }}

        public async Task {fieldname}_RemoveAsync(int index, int timeout=5000)
        {{
            var size = {fieldname}.Count;

            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), null, null, index, Tabula.SharedObjectMap.UpdateType.RemoveItem), false);
        
            var sw = new Stopwatch();
			while (sw.ElapsedMilliseconds < timeout)
			{{
				if ({fieldname}.Count != size)
                    return;

				await Task.Delay(10);
			}}

			throw new TimeoutException(""{fieldname}_RemoveAsync() timeout"");
        }}

        public void {fieldname}_Clear()
        {{
            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), null, null, null, Tabula.SharedObjectMap.UpdateType.ClearItems), false);
        }}

        public async Task {fieldname}_ClearAsync(int timeout=5000)
        {{
            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), null, null, null, Tabula.SharedObjectMap.UpdateType.ClearItems), false);
            
            var sw = new Stopwatch();
			while (sw.ElapsedMilliseconds < timeout)
            {{
                if ({fieldname}.Count == 0)
                    return;

                await Task.Delay(10);
            }}

			throw new TimeoutException(""{fieldname}_ClearAsync() timeout"");
        }}

        #region {fieldname} View collection updates

        private void _{fieldname}_viewcoll_add(int index,{elementtype}{nullable_questionmark} item)
        {{
            var coll = {fieldname};    // makes sure the backing collection is not null

            var v = item == null ? null : new {viewclass}(Model.__guid, nameof({fieldname}), (index==-1 ? _{fieldname}.Count : index));
            if (index == -1)
            {{
                _{fieldname}.Add(v);
                index = _{fieldname}.Count - 1;
            }}
            else
                _{fieldname}.Insert(index, v);

            for (int i = 0; i < _{fieldname}.Count; i++)
                _{fieldname}[i].__item_index = i;

            {fieldname} = _{fieldname}; // only setting will trigger binding

            NotifyViewUpdate(false, this, nameof({fieldname}), index, Tabula.SharedObjectMap.UpdateType.AddItem);
        }}

        private void _{fieldname}_viewcoll_remove(int index)
        {{
            //NOTE: BUG: it seems removing from the client updates the collection before this is called only in last index?
            try
            {{

            var coll = {fieldname};    // makes sure the backing collection is not null

            _{fieldname}.RemoveAt(index);

            for (int i = 0; i < _{fieldname}.Count; i++)
                _{fieldname}[i].__item_index = i;

            {fieldname} = _{fieldname}; // only setting will trigger binding
            }}
            catch
            {{
                // ignore the exception
            }}

            NotifyViewUpdate(false, this, nameof({fieldname}), index, Tabula.SharedObjectMap.UpdateType.RemoveItem);
        }}

        #endregion
");

output.Append($@"

        private ObservableCollection<{viewclass}> _{fieldname};
        [DoNotCheckEquality]
        public ObservableCollection<{viewclass}> {fieldname}
        {{
            get
            {{
                
                if (_{fieldname} == null && Model.{fieldname} != null)
                {{
                    _{fieldname} = new ObservableCollection<{viewclass}>();
                    for (int i=0; i<Model.{fieldname}.{collection_length}; i++)
                    {{
                        var v = new {viewclass}(Model.__guid, nameof({fieldname}), i);
                        _{fieldname}.Add(v);
                    }}
                }}

                return _{fieldname};
            }}

            set
            {{
                // setter is used only to reset or trigger binding
                if (value == null)
                    _{fieldname}=null;
                    
                RaisePropertyChanged(nameof({fieldname}));
                NotifyViewUpdate(false, this, nameof({fieldname}));
            }}
        }}

        public void _{fieldname}_update_item(bool from_server, int index) => _{fieldname}[index].OnItemUpdated(from_server, index);
");
                }

				#endregion

				// IDictionaries of primitives
				#region IDictionaries of primitive

				foreach (var f in view.IDictionaries)
                {
                    var fieldname = f.Item1;
                    var viewclass = f.Item2 + "View";
                    var dict_key_type = (f.Item3 as Type).FullName.Replace("+", ".");
                    var dict_value_type = (f.Item4 as Type).FullName.Replace("+", ".");

                    output.Append($@"
    
        public void {fieldname}_Add({dict_key_type} key, {dict_value_type} item)
        {{
            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), typeof({dict_value_type}), item, key, Tabula.SharedObjectMap.UpdateType.AddItem), false);
        }}

        public async Task {fieldname}_AddAsync({dict_key_type} key, {dict_value_type} item, int timeout=5000)
        {{
            var size = {fieldname}.Count;

            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), typeof({dict_value_type}), item, key, Tabula.SharedObjectMap.UpdateType.AddItem), false);

			var sw = new Stopwatch();
			while (sw.ElapsedMilliseconds < timeout)
			{{
				if ({fieldname}.Count != size)
                    return;

				await Task.Delay(10);
			}}

			throw new TimeoutException(""{fieldname}_AddAsync() timeout"");
        }}

        public void {fieldname}_Remove({dict_key_type} key)
        {{
            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), null, null, key, Tabula.SharedObjectMap.UpdateType.RemoveItem), false);
        }}

        public async Task {fieldname}_RemoveAsync({dict_key_type} key, int timeout=5000)
        {{
            var size = {fieldname}.Count;

            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), null, null, key, Tabula.SharedObjectMap.UpdateType.RemoveItem), false);
        
            var sw = new Stopwatch();
			while (sw.ElapsedMilliseconds < timeout)
			{{
				if ({fieldname}.Count != size)
                    return;

				await Task.Delay(10);
			}}

			throw new TimeoutException(""{fieldname}_RemoveAsync() timeout"");
        }}

        public void {fieldname}_Clear()
        {{
            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), null, null, null, Tabula.SharedObjectMap.UpdateType.ClearItems), false);
        }}

        public async Task {fieldname}_ClearAsync(int timeout=5000)
        {{
            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), null, null, null, Tabula.SharedObjectMap.UpdateType.ClearItems), false);
            
            var sw = new Stopwatch();
			while (sw.ElapsedMilliseconds < timeout)
            {{
                if ({fieldname}.Count == 0)
                    return;

                await Task.Delay(10);
            }}

			throw new TimeoutException(""{fieldname}_ClearAsync() timeout"");
        }}

        #region {fieldname} View collection updates

        private void _{fieldname}_viewcoll_add({dict_key_type} key)
        {{
            var coll = {fieldname};    // makes sure the backing collection is not null

            var v = new {viewclass}(Model.__guid, nameof({fieldname}), key);
            _{fieldname}.Add(key, v);

            {fieldname} = _{fieldname}; // only setting will trigger binding

            NotifyViewUpdate(false, this, nameof({fieldname}), key, Tabula.SharedObjectMap.UpdateType.AddItem);
        }}

        private void _{fieldname}_viewcoll_remove({dict_key_type} key)
        {{
            //NOTE: BUG: it seems removing from the client updates the collection before this is called only in last index?
            try
            {{
            var coll = {fieldname};    // makes sure the backing collection is not null

            _{fieldname}.Remove(key);

            {fieldname} = _{fieldname}; // only setting will trigger binding
            }}
            catch
            {{
                // ignore the exception
            }}

            NotifyViewUpdate(false, this, nameof({fieldname}), key, Tabula.SharedObjectMap.UpdateType.RemoveItem);
        }}

        #endregion


        private ObservableDictionary<{dict_key_type},{viewclass}> _{fieldname};
        [DoNotCheckEquality]
        public ObservableDictionary<{dict_key_type},{viewclass}> {fieldname}
        {{
            get
            {{
                if (_{fieldname} == null && Model.{fieldname} != null)
                {{
                    _{fieldname} = new ObservableDictionary<{dict_key_type},{viewclass}>();
                    foreach (var kvp in Model.{fieldname})
                    {{
                        var v = new {viewclass}(Model.__guid, nameof({fieldname}), kvp.Key);
                        _{fieldname}.Add(kvp.Key, v);
                    }}
                }}

                return _{fieldname};
            }}

            set
            {{
                // setter is used only to reset or trigger binding
                if (value == null)
                    _{fieldname}=null;

                RaisePropertyChanged(nameof({fieldname}));
                NotifyViewUpdate(false, this, nameof({fieldname}));
            }}
        }}

        public void _{fieldname}_update_item(bool from_server, {dict_key_type} key) => _{fieldname}[key].OnItemUpdated(from_server, key);
");
                }

				#endregion

				// IDictionaries of GuidObjects
				#region IDictionaries of GuidObjects

				foreach (var f in view.Guidobjects_IDictionaries)
                {
                    var fieldname = f.Item1;
                    var dict_key_type = (f.Item2 as Type).FullName.Replace("+", ".");
                    var dict_value_type_view = f.Item3.Replace("+", ".");
                    var dict_value_type = dict_value_type_view.Replace("View", ""); // ugly

                    output.Append($@"
        public long {fieldname}_Add({dict_key_type} key, {dict_value_type} item)
        {{
            var res = Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), typeof({dict_value_type}), item, key, Tabula.SharedObjectMap.UpdateType.AddItem), true);
            return _return_guid(res); 
        }}

        public async Task<{dict_value_type_view}> {fieldname}_AddAsync({dict_key_type} key, {dict_value_type} item, int index = -1, int timeout=5000)
        {{
            var res = Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), typeof({dict_value_type}), item, key, Tabula.SharedObjectMap.UpdateType.AddItem), true);
            var new_guid = _return_guid(res);

			var sw = new Stopwatch();
			while (sw.ElapsedMilliseconds < timeout)
			{{
				var v = Tabula.SharedObjectMap.SharedObjectMap.getViewFromGuid(new_guid);
                if (v != null && v is {dict_value_type_view})
                    return v as {dict_value_type_view};

				await Task.Delay(10);
			}}

			throw new TimeoutException(""{fieldname}_AddAsync() timeout"");
        }}

        public void {fieldname}_Remove({dict_key_type} key)
        {{
            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), null, null, key, Tabula.SharedObjectMap.UpdateType.RemoveItem), false);
        }}

        public async Task {fieldname}_RemoveAsync({dict_key_type} key, int timeout=5000)
        {{
            // supports also polymorphic collections marked as [GuidObjectCollection]
			long v_guid = ({fieldname}[key] as IGuidObjectSyncView).GetModel().__guid;

            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), null, null, key, Tabula.SharedObjectMap.UpdateType.RemoveItem), false);
        
            // wait until this guid disappears
			var sw = new Stopwatch();
			while (sw.ElapsedMilliseconds < timeout)
			{{
                if (!Tabula.SharedObjectMap.SharedObjectMap.TryGetObject(v_guid, out Tabula.SharedObjectMap.GuidObject obj))
                    return;

				await Task.Delay(10);
			}}

            throw new TimeoutException(""{fieldname}_RemoveAsync() timeout"");
        }}

        public void {fieldname}_Clear()
        {{
            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), null, null, null, Tabula.SharedObjectMap.UpdateType.ClearItems), false);
        }}

        public async Task {fieldname}_ClearAsync(int timeout=5000)
        {{
            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), null, null, null, Tabula.SharedObjectMap.UpdateType.ClearItems), false);
            
            var sw = new Stopwatch();
			while (sw.ElapsedMilliseconds < timeout)
            {{
                if ({fieldname}.Count == 0)
                    return;

                await Task.Delay(10);
            }}

			throw new TimeoutException(""{fieldname}_ClearAsync() timeout"");
        }}

        #region {fieldname} View collection updates

        private void _{fieldname}_viewcoll_add({dict_key_type} key, {dict_value_type} item)
        {{
            var coll = {fieldname};    // makes sure the backing collection is not null

            var v = item == null ? null : Tabula.SharedObjectMap.SharedObjectMap.CreateView(item, Model.__view, _{fieldname});
            _{fieldname}.Add(key, ({dict_value_type_view}) v);

            {fieldname} = _{fieldname}; // only setting will trigger binding

            NotifyViewUpdate(false, this, nameof({fieldname}), key, Tabula.SharedObjectMap.UpdateType.AddItem);
        }}

        private void _{fieldname}_viewcoll_remove({dict_key_type} key)
        {{
            //NOTE: BUG: it seems removing from the client updates the collection before this is called only in last index?
            try
            {{
            var coll = {fieldname};    // makes sure the backing collection is not null        

            _{fieldname}.Remove(key);

            {fieldname} = _{fieldname}; // only setting will trigger binding
            }}
            catch
            {{
                // ignore it
            }}

            NotifyViewUpdate(false, this, nameof({fieldname}), key, Tabula.SharedObjectMap.UpdateType.RemoveItem);
        }}

        #endregion

        private ObservableDictionary<{dict_key_type},{dict_value_type_view}> _{fieldname};
        [DoNotCheckEquality]
        public ObservableDictionary<{dict_key_type},{dict_value_type_view}> {fieldname}
        {{
            get
            {{
                if (_{fieldname} == null && Model.{fieldname} != null)
                {{
                    _{fieldname} = new ObservableDictionary<{dict_key_type},{dict_value_type_view}>();
                    foreach (var kvp in Model.{fieldname})
                    {{
                        // var v = new {dict_value_type_view}(kvp.Value, Model.__view, _{fieldname});                 
                        var v = Tabula.SharedObjectMap.SharedObjectMap.CreateView(kvp.Value, Model.__view, _{fieldname});
                        _{fieldname}.Add(kvp.Key, ({dict_value_type_view}) v);
                    }}
                }}

                return _{fieldname};
            }}

            set
            {{
                // setter is used only to reset or trigger binding
                if (value == null)
                    _{fieldname}=null;
              
                RaisePropertyChanged(nameof({fieldname}));
                NotifyViewUpdate(false, this, nameof({fieldname}));
            }}
        }}
        
");
                }

				#endregion

				// IList / Array of Guidobject
				#region IList / Array of Guidobject

				foreach (var f in view.Guidobjects_ILists)
                {
                    var fieldname = f.Item1;
                    var fieldtype = f.Item2;
                    var elementtype_view = f.Item3.Replace("+", ".");
                    var elementtype = elementtype_view.Replace("View", ""); // ugly
                    string collection_length = (fieldtype.IsArray ? "Length" : "Count");

                    output.Append($@"

        public long {fieldname}_Add({elementtype} item, int index = -1)
        {{
            var res = Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), typeof({elementtype}), item, index, Tabula.SharedObjectMap.UpdateType.AddItem), true);
            return _return_guid(res); 
        }}

        public async Task<{elementtype_view}> {fieldname}_AddAsync({elementtype} item, int index = -1, int timeout=5000)
        {{
            var res = Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), typeof({elementtype}), item, index,  Tabula.SharedObjectMap.UpdateType.AddItem), true);
            var new_guid = _return_guid(res);

			var sw = new Stopwatch();
			while (sw.ElapsedMilliseconds < timeout)
			{{
				var v = Tabula.SharedObjectMap.SharedObjectMap.getViewFromGuid(new_guid);
                if (v != null && v is {elementtype_view})
                    return v as {elementtype_view};

				await Task.Delay(10);
			}}

			throw new TimeoutException(""{fieldname}_AddAsync() timeout"");
        }}

        public void {fieldname}_Remove(int index)
        {{
            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), typeof({elementtype}), null, index, Tabula.SharedObjectMap.UpdateType.RemoveItem), false);
        }}

        public async Task {fieldname}_RemoveAsync(int index, int timeout=5000)
        {{
            // supports also polymorphic collections marked as [GuidObjectCollection]
			long v_guid = ({fieldname}[index] as IGuidObjectSyncView).GetModel().__guid;

            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), typeof({elementtype}), null, index, Tabula.SharedObjectMap.UpdateType.RemoveItem), false);
        
            // wait until this guid disappears
			var sw = new Stopwatch();
			while (sw.ElapsedMilliseconds < timeout)
			{{
                if (!Tabula.SharedObjectMap.SharedObjectMap.TryGetObject(v_guid, out Tabula.SharedObjectMap.GuidObject obj))
                    return;

				await Task.Delay(10);
			}}

            throw new TimeoutException(""{fieldname}_RemoveAsync() timeout"");
        }}

        public void {fieldname}_Clear()
        {{
            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), null, null, null, Tabula.SharedObjectMap.UpdateType.ClearItems), false);
        }}

        public async Task {fieldname}_ClearAsync(int timeout=5000)
        {{
            Tabula.SharedObjectMap.SharedObjectMap.Sync(Tabula.SharedObjectMap.GuidUpdate.ToServer(Model.__guid, nameof({fieldname}), null, null, null, Tabula.SharedObjectMap.UpdateType.ClearItems), false);
            
            var sw = new Stopwatch();
			while (sw.ElapsedMilliseconds < timeout)
            {{
                if ({fieldname}.Count == 0)
                    return;

                await Task.Delay(10);
            }}

			throw new TimeoutException(""{fieldname}_ClearAsync() timeout"");
        }}

        #region {fieldname} View collection updates        

        private void _{fieldname}_viewcoll_add(int index, {elementtype} item)
		{{
            var coll = {fieldname};    // makes sure the backing collection is not null

            var v = item == null ? null : Tabula.SharedObjectMap.SharedObjectMap.CreateView(item, Model.__view, _{fieldname});
            if (index == -1)
            {{
                _{fieldname}.Add(({elementtype_view}) v);
                index = _{fieldname}.Count - 1;
            }}
            else
                _{fieldname}.Insert(index, ({elementtype_view}) v);

            {fieldname} = _{fieldname}; // only setting will trigger binding

            NotifyViewUpdate(false, this, nameof({fieldname}), index, Tabula.SharedObjectMap.UpdateType.AddItem);
        }}
        
        private void _{fieldname}_viewcoll_remove(int index)
        {{
            //NOTE: BUG: it seems removing from the client updates the collection before this is called only in last index?
            try
            {{
            var coll = {fieldname};    // makes sure the backing collection is not null

            _{fieldname}.RemoveAt(index);

            {fieldname} = _{fieldname}; // only setting will trigger binding
            }}
            catch
            {{
                // ignore it
            }}

            NotifyViewUpdate(false, this, nameof({fieldname}), index, Tabula.SharedObjectMap.UpdateType.RemoveItem);
        }}

        #endregion


        private ObservableCollection<{elementtype_view}> _{fieldname};
        [DoNotCheckEquality]
        public ObservableCollection<{elementtype_view}>   {fieldname}
        {{
            get
            {{
                if (_{fieldname} == null && Model.{fieldname} != null)
                {{
                    _{fieldname} = new ObservableCollection<{elementtype_view}>();
                    foreach (var i in Model.{fieldname})
                    {{                      
                        var v = i == null ? null : Tabula.SharedObjectMap.SharedObjectMap.CreateView(i, Model.__view, _{fieldname});
                        _{fieldname}.Add(({elementtype_view}) v);
                    }}
                }}

                return _{fieldname};
            }}
           
            set
            {{
                // setter is used only to reset or trigger binding
                if (value == null)
                    _{fieldname}=null;

                RaisePropertyChanged(nameof({fieldname}));
                NotifyViewUpdate(false, this, nameof({fieldname}));
            }}
        }}        

         public void _{fieldname}_update_item(bool from_server, int index) => OnItemUpdated(from_server, nameof({fieldname}), index);
");
                }

				#endregion

				// Ignored fields
				#region Ignored fields

				foreach (var f in view.Ignored)
                {
                    var fieldname = f.Item1;
                    var fieldtype = (f.Item2 as Type).FullName.Replace("+", ".");

                    output.Append($"        // IGNORED: {fieldtype} {fieldname}");
                }

				#endregion

				// Callable methods, ones marked with GuidObjectCallableAttribute
				#region GuidObjectCallable methods

				foreach (var m in (from om in view.CallableMethods orderby om.Name select om))
                {
                    // exclude directsync for methods that exclude it
                    var guidobject_callable_attribute = m.GetCustomAttribute<GuidObjectCallableAttribute>(true);
                    UpdateFlags update_flags = (guidobject_callable_attribute.NoDirectSync ? UpdateFlags.NoDirectSync : UpdateFlags.None);

					//Attribute.IsDefined(Type.GetType($"{NameSpace}.{modelname}").GetField(fieldname), typeof(GuidObjectFieldAttribute));

					// m.Name
					var args_types = (from parameter in m.GetParameters() select parameter.ParameterType).ToArray();
                    var args_types_strings = (from parameter in m.GetParameters() select $"\"{parameter.ParameterType.ToString()}\"").ToArray();
					var args_names = (from parameter in m.GetParameters() select parameter.Name).ToArray();
                    var default_args = (from parameter in m.GetParameters() select parameter.DefaultValue).ToArray();
                    var returntype = m.ReturnType;

                    var args_string_array = new List<string>();
                    for (int i = 0; i < args_names.Length; i++)
                    {
                        bool has_default_arg = (default_args[i]==null || (default_args[i]!=null && default_args[i].GetType() != typeof(System.DBNull)));

                        if (has_default_arg)
                        {
                            if (default_args[i]==null)
                                args_string_array.Add($"{args_types[i]} {args_names[i]}=null");
                            else if (default_args[i].GetType() == typeof(string))
                                args_string_array.Add($"{args_types[i]} {args_names[i]}=\"{default_args[i].ToString()}\"");
                            else 
                                args_string_array.Add($"{args_types[i]} {args_names[i]}={default_args[i].ToString()}");
                        }
                        else
                            args_string_array.Add($"{args_types[i]} {args_names[i]}");
                    }

                    string returntask = (returntype != typeof(void) ? $"Task<{returntype}>" : "Task");

                    output.Append($@"
        public async {returntask} {m.Name}({string.Join(",", args_string_array)})
        {{
            Tabula.SharedObjectMap.SingleUpdateResult result;

            result = await Task.Run(() =>
            Tabula.SharedObjectMap.SharedObjectMap.Sync(
                Tabula.SharedObjectMap.GuidUpdate.ToServerCall(Model.__guid,
                ""{m.Name}"",
                new object[] {{ {string.Join(",", args_names)} }},
                new string[] {{ {string.Join(",", args_types_strings)} }},
                flags: Tabula.SharedObjectMap.UpdateFlags.{update_flags}),
                {(returntype!=typeof(void) ? "true" : "false")}));

            {(returntype != typeof(void) ? $"return ({returntype})  Tabula.SharedObjectMap.SharedObjectMap.ConvertAny(result.ChangedValue, typeof({returntype}));" : "")}           
         }}        
");
                }

				#endregion

				// close view
				output.Append(" }\n\n");
            }

            // close namespace
            output.Append("\n}");

            File.WriteAllText(output_file, output.ToString());
        }

        private static void _GenerateViewsFromInstance(Type instance_type, List<ModelDefinition> modelmap, MemberInfo member = null)
        {
            var instance_name = instance_type.Name;

            var existing_instance = (from m in modelmap where m.Class == instance_name select m).FirstOrDefault();

            //if (modelmap.ContainsKey(instance_name))
            if (existing_instance != null)
                return;

            if (instance_type.Equals(typeof(object)))
                return;

            var view = new ModelDefinition()
            {
                 Class = instance_name,
                 BaseClass = $"GuidObjectSyncView<{instance_name}>",
                 IsValueView = false
            };

            modelmap.Add(view);

            // Scan Fields
            FieldInfo[] fields = instance_type.GetFields(BindingFlags.Public | BindingFlags.Instance);

            // Looking for: Objects, Arrays, IList, IDictionary
            foreach (var f in fields)
            {
                // skip special fields
                if (_is_field_special(f.Name))
                    continue;

                // skip XmlIgnore attributes
                if (Attribute.IsDefined(f, typeof(XmlIgnoreAttribute))) /*||
                    Attribute.IsDefined(f, typeof(JsonIgnoreAttribute)))*/
                    continue;

                bool force_guidobject_collection = Attribute.IsDefined(f, typeof(GuidObjectCollectionAttribute));

                if (f.FieldType.IsArray || (f.FieldType.IsGenericType && typeof(IList).IsAssignableFrom(f.FieldType)))
                {
                    // Array or IList

                    var element_type = (f.FieldType.IsGenericType ? f.FieldType.GenericTypeArguments[0] : f.FieldType.GetElementType());

                    if (element_type.IsSubclassOf(typeof(GuidObject)) || force_guidobject_collection)
                    {
                        // Guidobjects_ILists

                        view.Structure.Guidobjects_ILists.Add(new Tuple<string, Type, string>(f.Name, f.FieldType, element_type.FullName+(force_guidobject_collection ? "" : "View")));
                        _GenerateViewsFromInstance(element_type, modelmap);
                    }
                    else
                    {
                        // ILists

                        // private ObservableCollection<SyncListItemView<{elementtype}>> _{fieldname};

                        // add the value-type view
                        var value_view = new ModelDefinition()
                        {
                            Class = $"{view.Class}_{f.Name}",   //es: Model_List2
                            BaseClass = $"SyncListItemView<{element_type}>",
                            IsValueView = true
                        };

                        modelmap.Add(value_view);

                        view.Structure.ILists.Add(new Tuple<string, string, Type, Type>(f.Name, value_view.Class, f.FieldType, element_type));
                    }
                }
                else if (f.FieldType.IsGenericType && (typeof(IDictionary).IsAssignableFrom(f.FieldType)))
                {
                    // view.Ignored.Add(new Tuple<string, Type>(f.Name, f.FieldType));

                    Type dict_keytype = f.FieldType.GetGenericArguments()[0];
                    Type dict_valuetype = f.FieldType.GetGenericArguments()[1];

                    if (dict_valuetype.IsSubclassOf(typeof(GuidObject)) || force_guidobject_collection)
                    {
                        // Guidobjects_IDictionaries

                        view.Structure.Guidobjects_IDictionaries.Add(new Tuple<string, Type, string>(f.Name, dict_keytype, dict_valuetype.FullName+(force_guidobject_collection ? "" : "View")));
                        _GenerateViewsFromInstance(dict_valuetype, modelmap);
                    }
                    else
                    {
                        // IDictionaries

                        // add the value-type view
                        var value_view = new ModelDefinition()
                        {
                            Class = $"{view.Class}_{f.Name}",   
                            BaseClass = $"SyncDictionaryItemView<{dict_valuetype}>",
                            IsValueView = true
                        };

                        modelmap.Add(value_view);

                        view.Structure.IDictionaries.Add(new Tuple<string, string, Type, Type>(f.Name, value_view.Class, dict_keytype, dict_valuetype));
                    }

                }
                else if (f.FieldType.IsClass)
                {
                    // Ref classes (also String..) or GuidObjects 

                    if (f.FieldType.IsSubclassOf(typeof(GuidObject)))
                    {
                        // Guidobjects

                        view.Structure.Guidobjects.Add(new Tuple<string, string>(f.Name, f.FieldType.FullName+"View"));
                        _GenerateViewsFromInstance(f.FieldType, modelmap);
                    }
                    else
                    {
                       // Fields

                        view.Structure.Fields.Add(new Tuple<string, Type>(f.Name, f.FieldType));
                    }
                }
                else if (IsPrimitive(f.FieldType) || f.FieldType.IsValueType)
                {
                    // Fields

                    view.Structure.Fields.Add(new Tuple<string, Type>(f.Name, f.FieldType));
                }
                else
                {
                    // IGNORED 

                    view.Structure.Ignored.Add(new Tuple<string, Type>(f.Name, f.FieldType));
                }
            }

            // Callable methods, ones marked with GuidObjectCallableAttribute
            view.Structure.CallableMethods = instance_type.GetMethods(BindingFlags.Public | BindingFlags.Instance | BindingFlags.FlattenHierarchy).Where(m => m.GetCustomAttributes(typeof(GuidObjectCallableAttribute), false).Length > 0).ToList();

        }

        // WRAPPER (SERVER SIDE) GENERATION

        public static void GenerateWrappersFromInstance(Type instance_type, string ModelNameSpace, string output_file)
        {
            var output = new StringBuilder();
            
            var model_map = new Dictionary<string, ModelStructure>();

            lock (model_map)
            {
                _GenerateWrappersFromInstance(instance_type, model_map);

                // Make sure classes flagged with GuidObjectClassAttribute are always there
                var types_to_include = GetTypesWithAttributeAllAssemblies(typeof(GuidObjectClassAttribute)).ToList();
                foreach (var t in types_to_include)
                    _GenerateWrappersFromInstance(t, model_map);
            }

            // scan model_map and generate code
            output.Append($@"using System;
using System.Collections.Generic;
using Tabula.SharedObjectMap;

// Wrappers for {instance_type} auto-generated on: {DateTime.Now.ToString()}

namespace {ModelNameSpace}
{{");
            // Generate WrappersExtensions extension methods helpers
            output.Append($@"
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public static partial class WrappersExtensions
    {{");

            foreach (var kvp in model_map)
            {
                var modelname = kvp.Key;
                var view = kvp.Value;

                output.Append($@"
        public static {modelname}Wrapper getWrapper(this {modelname} m) => Tabula.SharedObjectMap.SharedObjectMap.getWrapperFromModel<{modelname}, {modelname}Wrapper>(m);");
            }

                output.Append($@"
    }}
");


            foreach (var kvp in model_map)
            {
                var modelname = kvp.Key;
                var view = kvp.Value;

                // open view
                output.Append($@"
    [Tabula.SharedObjectMap.Obfuscation_Skip]
    [System.Reflection.ObfuscationAttribute(Exclude=true)]
    public partial class {modelname}Wrapper : WrapperBase<{modelname}>
    {{
        public static implicit operator {modelname}Wrapper({modelname} m)
        {{
            var w = new {modelname}Wrapper(m);
            return w;
        }}

        public {modelname}Wrapper() : base(null)
        {{}}

        public {modelname}Wrapper({modelname} m) : base(m)
        {{}}

        // helper to invalidate a collection (wrapper)
        public bool _invalidateCollection(string name)
        {{
            switch(name)
            {{
");
                var collections = new List<string>();
                foreach (var f in view.ILists)
                    collections.Add(f.Item1);
                foreach (var f in view.IDictionaries)
                    collections.Add(f.Item1);
                foreach (var f in view.Guidobjects_ILists)
                    collections.Add(f.Item1);
                foreach (var f in view.Guidobjects_IDictionaries)
                    collections.Add(f.Item1);

                // single invalidate
                foreach (var f in collections)
                    output.AppendLine($@"               case nameof({f}): _{f}=null; return true;");

                // invalidate all
                output.AppendLine($@"               case null:");
                foreach (var f in collections)
                    output.AppendLine($@"                   _{f}=null;");

                output.Append($@"                   return true;
            }}

            return false;
        }}
");
                
                // primitive fields
                foreach (var f in view.Fields)
                {
                    var fieldname = f.Item1;
                    var fieldtype = (f.Item2 as Type).FullName.Replace("+", ".");

                    output.Append($@"
        public {fieldtype} {fieldname}
        {{
            get => _Model.{fieldname};
                   
            set
            {{
                _Model.{fieldname} = value;
                _Model.CommitUpdate(nameof({fieldname}));
            }}
        }}

");

                }



                // Guidobjects fields
                foreach (var f in view.Guidobjects)
                {
                    var fieldname = f.Item1;
                    var fieldtype = f.Item2.Replace("+", ".");

                    output.Append($@"
        private {fieldtype}Wrapper _{fieldname};
        public {fieldtype}Wrapper {fieldname}
        {{
            get
            {{
                if (_{fieldname} == null)
                    _{fieldname} = new {fieldtype}Wrapper(_Model.{fieldname});

                return _{fieldname};
            }}

            set
            {{
                Tabula.SharedObjectMap.SharedObjectMap.AddAllGuidObjects(value._Model);
                _{fieldname} = value;
                Tabula.SharedObjectMap.SharedObjectMap.RemoveAllGuidObjects(_Model.{fieldname});
                _Model.{fieldname} = _{fieldname}._Model;
                _Model.{fieldname}.__guid_parent = this._Model.__guid;
                _Model.CommitUpdate(nameof({fieldname}));            
            }}
        }}
");
                }

                // ILists of primitives
                foreach (var f in view.ILists)
                {
                    var fieldname = f.Item1;
                    var viewclass = f.Item2;
                    var fieldtype = f.Item3;
                    var elementtype = (f.Item4 as Type).FullName;
                    string collection_length = (fieldtype.IsArray ? "Length" : "Count");

                    output.Append($@"
        private WrapperList<{elementtype}> _{fieldname};
        public WrapperList<{elementtype}> {fieldname}
        {{
            get
            {{
                if (_{fieldname} == null)
                    _{fieldname} = new  WrapperList<{elementtype}>(_Model.{fieldname}, _Model, ""{fieldname}"");

                return _{fieldname};
            }}
        }}

        public void {fieldname}_Set(List<{elementtype}> list)
		{{
            CommitBegin();
            {fieldname}.Clear();
            foreach (var i in list)
                {fieldname}.Add(i);
            CommitEnd();
		}}
");
                }

                // IDictionaries of primitives
                foreach (var f in view.IDictionaries)
                {
                    var fieldname = f.Item1;
                    var viewclass = f.Item2;
                    var dict_key_type = (f.Item3 as Type).FullName;
                    var dict_value_type = (f.Item4 as Type).FullName;

                    output.Append($@"
        private WrapperDictionary<{dict_key_type},{dict_value_type}> _{fieldname};
        public WrapperDictionary<{dict_key_type},{dict_value_type}> {fieldname}
        {{
            get
            {{
                if (_{fieldname} == null)
                    _{fieldname} = new  WrapperDictionary<{dict_key_type},{dict_value_type}>(_Model.{fieldname}, _Model, ""{fieldname}"");

                return _{fieldname};
            }}
        }}  

        public void {fieldname}_Set(Dictionary<{dict_key_type},{dict_value_type}> dict)
        {{
            CommitBegin();
            {fieldname}.Clear();
            foreach (var kvp in dict)
                {fieldname}.Add(kvp.Key, kvp.Value);
            CommitEnd();
        }}
");
                }

                // IDictionaries of GuidObjects
                foreach (var f in view.Guidobjects_IDictionaries)
                {
                    var fieldname = f.Item1;
                    var dict_key_type = (f.Item2 as Type).FullName;
                    var dict_value_type = f.Item3;

                    output.Append($@"
        private WrapperGuidObjectDictionary<{dict_key_type},{dict_value_type},{dict_value_type}Wrapper> _{fieldname};
        public WrapperGuidObjectDictionary<{dict_key_type},{dict_value_type},{dict_value_type}Wrapper> {fieldname}
        {{
            get
            {{
                if (_{fieldname} == null)
                    _{fieldname} = new  WrapperGuidObjectDictionary<{dict_key_type},{dict_value_type},{dict_value_type}Wrapper>(_Model.{fieldname}, _Model, ""{fieldname}"");

                return _{fieldname};
            }}
        }} 

        public void {fieldname}_Set(Dictionary<{dict_key_type},{dict_value_type}> dict)
        {{
            CommitBegin();
            {fieldname}.Clear();
            foreach (var kvp in dict)
                {fieldname}.Add(kvp.Key, kvp.Value);
            CommitEnd();
        }}
");
                }

                // IList / Array of Guidobject
                foreach (var f in view.Guidobjects_ILists)
                {
                    var fieldname = f.Item1;
                    var fieldtype = f.Item2;
                    var elementtype = f.Item3;
                    string collection_length = (fieldtype.IsArray ? "Length" : "Count");

                    output.Append($@"
        private WrapperGuidObjectList<{elementtype}, {elementtype}Wrapper> _{fieldname};
        public WrapperGuidObjectList<{elementtype}, {elementtype}Wrapper> {fieldname}
        {{
            get
            {{
                if (_{fieldname} == null)                                    
                    _{fieldname} = new WrapperGuidObjectList<{elementtype}, {elementtype}Wrapper>(_Model.{fieldname}, _Model, ""{fieldname}"");                

                return _{fieldname};
            }}
        }}

        public void {fieldname}_Set(List<{elementtype}> list)
		{{
            CommitBegin();
            {fieldname}.Clear();
            foreach (var i in list)
                {fieldname}.Add(i);
            CommitEnd();
		}}
");
                }

                // Ignored fields
                foreach (var f in view.Ignored)
                {
                    var fieldname = f.Item1;
                    var fieldtype = (f.Item2 as Type).FullName;

                    output.Append($"        // IGNORED: {fieldtype} {fieldname}");
                }


                // close view
                output.Append(" }\n\n");
            }

            // close namespace
            output.Append("\n}");

            File.WriteAllText(output_file, output.ToString());
        }

        private static void _GenerateWrappersFromInstance(Type instance_type, Dictionary<string, ModelStructure> modelmap, MemberInfo member = null)
        {
            var instance_name = instance_type.Name;

            if (modelmap.ContainsKey(instance_name))
                return;

            var model = new ModelStructure();
            modelmap.Add(instance_name, model);

            // Scan Fields
            FieldInfo[] fields = instance_type.GetFields(BindingFlags.Public | BindingFlags.Instance);

            // Looking for: Objects, Arrays, IList, IDictionary
            foreach (var f in fields)
            {
                // skip special fields
                if (_is_field_special(f.Name))
                    continue;

                // skip XmlIgnore attributes
                if (Attribute.IsDefined(f, typeof(XmlIgnoreAttribute))) /*||
                    Attribute.IsDefined(f, typeof(JsonIgnoreAttribute)))*/
                    continue;

                if (f.FieldType.IsArray || (f.FieldType.IsGenericType && typeof(IList).IsAssignableFrom(f.FieldType)))
                {
                    // Array or IList

                    var element_type = (f.FieldType.IsGenericType ? f.FieldType.GenericTypeArguments[0] : f.FieldType.GetElementType());

                    if (element_type.IsSubclassOf(typeof(GuidObject)))
                    {
                        model.Guidobjects_ILists.Add(new Tuple<string, Type, string>(f.Name, f.FieldType, element_type.FullName));
                        _GenerateWrappersFromInstance(element_type, modelmap);
                    }
                    else
                    {
                        model.ILists.Add(new Tuple<string, string, Type, Type>(f.Name, "", f.FieldType, element_type));
                    }
                }
                else if (f.FieldType.IsGenericType && (typeof(IDictionary).IsAssignableFrom(f.FieldType)))
                {
                    // view.Ignored.Add(new Tuple<string, Type>(f.Name, f.FieldType));

                    Type dict_keytype = f.FieldType.GetGenericArguments()[0];
                    Type dict_valuetype = f.FieldType.GetGenericArguments()[1];

                    if (dict_valuetype.IsSubclassOf(typeof(GuidObject)))
                    {
                        model.Guidobjects_IDictionaries.Add(new Tuple<string, Type, string>(f.Name, dict_keytype, dict_valuetype.FullName));
                        _GenerateWrappersFromInstance(dict_valuetype, modelmap);
                    }
                    else
                    {
                        model.IDictionaries.Add(new Tuple<string, string, Type, Type>(f.Name, "", dict_keytype, dict_valuetype));
                    }

                }
                else if (f.FieldType.IsClass)
                {
                    // Ref classes (also String..) or GuidObjects 

                    if (f.FieldType.IsSubclassOf(typeof(GuidObject)))
                    {
                        model.Guidobjects.Add(new Tuple<string, string>(f.Name, f.FieldType.FullName));
                        _GenerateWrappersFromInstance(f.FieldType, modelmap);
                    }
                    else
                    {
                        model.Fields.Add(new Tuple<string, Type>(f.Name, f.FieldType));
                    }
                }
                else if (IsPrimitive(f.FieldType) || f.FieldType.IsValueType)
                {
                    model.Fields.Add(new Tuple<string, Type>(f.Name, f.FieldType));
                }
                else
                {
                    // IGNORED 

                    model.Ignored.Add(new Tuple<string, Type>(f.Name, f.FieldType));
                }
            }
        }


        // HELPERS

        public static object ConvertFromJson(object value, string type)
        {
            if (value is JToken)
                return (value as JToken).ToObject(SharedObjectMap.GetTypeAllAssemblies(type));
            else
                return value;            
        }

        public static object ConvertFromJson(object value, Type type)
        {
            if (value is JToken)
                return (value as JToken).ToObject(type);
            else if (type.IsEnum)
                return Enum.ToObject(type, value);
            else if (value.GetType() != type)           // necessary: same class could not be IConvertible
                return Convert.ChangeType(value, type);
            else
                return value;
        }

        public static object ConvertAny(object value, Type type)
        {
            if (value == null)
                return null;

            object converted;

            // handles complex objects first, as well as normal primitives
            converted = ConvertFromJson(value, type);

            // other conversion (ex: int64 -> int32)
            if (converted.GetType() != type)
                converted = Convert.ChangeType(value, type);

            return converted;
        }

        // Standard method calling with parameter conversion
        public static object CallMethod(object obj_ref, MethodInfo method, IPEndPoint remote_ep, object[] args)
        {
            // normalize parameters
            if (args != null)
            {
                ParameterInfo[] parameters = method.GetParameters();
                for (int i = 0; i < args.Length; i++)
                {
                    // handles complex objects first, as well as normal primitives
                    args[i] = ConvertAny(args[i], parameters[i].ParameterType);

                    // IPEndPoint rewriting if it's first parameter
                    if (i == 0 && parameters[i].ParameterType == typeof(IPEndPoint))
                        args[i] = remote_ep;
                }
            }

            return method.Invoke(obj_ref, args);           
        }

        // will clean a json from __guid and __guid_parent
        // https://stackoverflow.com/questions/40116088/remove-fields-from-json-dynamically-using-json-net
        public static string CleanJsonFromGuids(string json)
        {
            void _clean_json(JToken token)
            {
                if (token.Type == JTokenType.Object)
                {
                    foreach (JProperty prop in token.Children<JProperty>().ToList())
                    {
                        if (prop.Name.StartsWith("__guid"))
                        {
                            prop.Remove();
                        }
                        else
                        {
                            _clean_json(prop.Value);
                        }
                    }
                }
                else if (token.Type == JTokenType.Array)
                {
                    foreach (JToken child in token.Children())
                    {
                        _clean_json(child);
                    }
                }
            }

            JToken _token = JToken.Parse(json);
            _clean_json(_token);
            return _token.ToString();
        }
    }

    // Used to flag classes to be always included/excluded from views/wrappers generation even if not used in model
    public class GuidObjectClassAttribute : Attribute 
    {
        public bool Include = true;
    }

    public class GuidObjectFieldAttribute : Attribute
    {
        public bool DirectSync = false;     // will sync directly with quick methods (OSC)
    }

    // Will promote a generic List<object> or Dictrionary<..,object> as if it was a GuidObject collection, allowing polymorphic types and runtime view creation
    public class GuidObjectCollectionAttribute : Attribute 
    { }

    // methods callable by SharedObjectMap_Client
    public class GuidObjectCallableAttribute : Attribute
    {
        public bool NoDirectSync = false;   // will use TCP only
    }

	// for extension methods
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public static partial class SharedObjectMapExtensions
    {
        public static async Task<GuidObject> waitForGuidObject(this long guid, int timeout = 5000, int delay = 16)
        {
            return await SharedObjectMap.waitForGuidObject(guid, timeout, delay);
        }

        public static async Task<bool> waitForGuidObjectNotFound(this long guid, int timeout = 5000, int delay = 16)
        {
            return await SharedObjectMap.waitForGuidObjectNotFound(guid, timeout, delay);
        }

        public static T CloneGuidObject<T>(this T source) where T : GuidObject, new()
        {
            return SharedObjectMap.CloneGuidObject(source);
        }

        public static void CopyGuidObjectFieldsTo(this GuidObject source, GuidObject dest, bool commit_updates, CopyFieldsOperation operation = CopyFieldsOperation.Default, List<string> field_list = null, List<string> changed_fields = null)
        {
            SharedObjectMap.CopyGuidObjectFieldsTo(source, dest, commit_updates, operation, field_list, changed_fields);
        }
    }

    // Base class for any model object whose reference is mapped to a guid for granular update of its fields
    [Obfuscation_Skip]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class GuidObject
    {
        // the object global identifier and its parent, generated by the scans and later tracked when using wrappers
        // they need to be serialized either in Xml and Json
        public long __guid = -1, __guid_parent = -1;

        // if __view is valorized this object is client-side
        // __parent is the parent GuidObject view
        // __container is the container class (IList, IDictionary etc.)

        // NOTE: added a backing field so that every time the __view is valorized the model can be notified (useful in interactive contexts)
        private IGuidObjectSyncView __view_field;   //private backing field
        [XmlIgnore]
        [JsonIgnore]
        public IGuidObjectSyncView __view
        {
            get => __view_field;

            set
            {
                var old_view = __view_field;

                __view_field = value;

                if (OnViewChanged!=null)
                {
                    if (old_view != value)
                        OnViewChanged(value);
                }
            }
        }

		[XmlIgnore]
		[JsonIgnore]
		public Action<IGuidObjectSyncView> OnViewChanged;   //the model can register to be notified that the view has changed

		[XmlIgnore]
		[JsonIgnore]
		public IGuidObjectSyncView __view_parent;

		// if valorized a client side view has been created for this model
		[XmlIgnore]
        [JsonIgnore]
        public object __view_container;

        // If valorized a server side wrapper has been created for this model
        [XmlIgnore]
        [JsonIgnore]
        public object __model_wrapper;

        [XmlIgnore]
        [JsonIgnore]
        public GuidObject __Parent
        {
            get
            {
                GuidObject p = null;
                if (__guid_parent != -1)
                    if (SharedObjectMap.TryGetObject(__guid_parent, out p))
                        return p;

                return null;
            }
        }

        // cache the object verbose path from rootmodel, since it is an expensive operation
        // NOTE: _path_cache MUST be invalidated in collections where items are removed
        [XmlIgnore]
		[JsonIgnore]
		public string __path_cache;
        [XmlIgnore]
        [JsonIgnore]
        public string __path
        {
            get
            {				
				if (string.IsNullOrEmpty(__path_cache))
                    __path_cache = SharedObjectMap.FindGuidObjectPathAsString(__guid);

                return __path_cache;                
            }
        }

        // Delayed commits (both views and wrappers)
        // NOTE: version 2 is simplified and only acts at the object scope, no inheritance
        [XmlIgnore]
        [JsonIgnore]
        public List<GuidUpdate> __delayed_updates;
        [XmlIgnore]
        [JsonIgnore]
        public bool __is_in_commit = false;

        // Events
        [XmlIgnore]
        [JsonIgnore]
        public Func<GuidUpdate, bool> onNotifyUpdate { get; set; } // return true to block the bubble up

        public GuidObject() //(bool add_to_map = false)
        {
            // Auto guid generation is deprecated, an SharedObjectMap.AddAllGuidObjects() is always needed on new objects
            /*
            if (add_to_map && SharedObjectMap.TrackNewGuidObjects)
            {
                __guid = SharedObjectMap.NextGuid;
                SharedObjectMap.Add(__guid, this);
            }
            */
        }        
            

        // returns the fieldname of this object in the parent object
        public string GetParentFieldName()
        {
            string path = SharedObjectMap.FindGuidObjectPathAsString(__guid);
            var path_array = path.Split('.');
            return path_array[path_array.Length - 1];
        }

        public void RemoveHierarchyFromSharedObjectMap()
        {
            SharedObjectMap.RemoveAllGuidObjects(this);
        }

        [XmlIgnore]
        [JsonIgnore]
        public bool IsInCommit => __is_in_commit || IsAncestorInCommit;

        // checks whether an ancestor is in commit
        [XmlIgnore]
        [JsonIgnore]
        public bool IsAncestorInCommit
        {
            get
            {
                // let's walk up the hierarchy to see if this or parents are in a commit
                GuidObject obj = this;

                while (true)
                {
                    if (obj.__guid_parent != -1)
                    {
                        obj = object_map[obj.__guid_parent];
                        if (obj.__is_in_commit)
                            return true;
                    }
                    else
                    {                        
                        return false;
                    }
                }
            }
        }

        // GuidObject always stores delayed updates locally, both for views and for wrappers
        // CommitEnd() implementation depends on the view / wrapper
        public void CommitBegin()
        {
            if (__is_in_commit || Settings.HasFlag(SettingsFlags.DisableCommit))
                return;   

            __is_in_commit = true;
        }

        public void AddDelayedUpdate(GuidUpdate update)
        {
            CommitBegin();

            if (__delayed_updates == null)
                __delayed_updates = new List<GuidUpdate>();

            try
            {
                lock (__delayed_updates)
                {
                    __delayed_updates.Add(update);
                }
            }
            catch(Exception ex)
            {
				log("AddDelayedUpdate() Exeption");
            }

            if (Debug)
                log($"AddUpdate Delayed count={__delayed_updates.Count}");
        }

        // Called from view/wrapper implementation to clear updates and reset status
        public void CommitEnd()
        {
            if (__delayed_updates != null)
                lock (__delayed_updates)
                {
                    __delayed_updates.Clear();
                    __delayed_updates = null;
                }

            __is_in_commit = false;
        }
       

        // will schedule an update of the entire object and hierarchy sending all the field values       
        public void CommitUpdate()
        {
            foreach (var f in SharedObjectMap.GetSerializedFields(this))
            {
               // TEST: sending nested GuidObjects is a recursive call to its commit
               // FIXME: what about collections of guidobjets?
               if (typeof(GuidObject).IsAssignableFrom(f.FieldType))
               {
                    GuidObject obj = (GuidObject) f.GetValue(this);
                    obj.CommitUpdate();
               }
               else
                    CommitUpdate(f);
            }
        }

        // will schedule an update of single field
        public bool CommitUpdate(string fieldname)
        {
            // retrieve field value with reflection 
            var f = GetType().GetField(fieldname, BindingFlags.Public | BindingFlags.Instance);
            if (f == null)
            {
                if (SharedObjectMap.Debug)
                    SharedObjectMap.log($"CommitUpdate: field {fieldname} not found");

                return false;
            }

            SharedObjectMap.AddUpdate(GuidUpdate.ToClient(__guid, fieldname, f.FieldType, CloneWithJson(f.GetValue(this))));

            return true;
        }

        // will schedule an update of single field
        public bool CommitUpdate(FieldInfo f)
        {
            SharedObjectMap.AddUpdate(GuidUpdate.ToClient(__guid, f.Name, f.FieldType, CloneWithJson(f.GetValue(this))));

            return true;
        }

        // will schedule an update about a single element in a collection (IList, Array or IDictionary). 
        // This is not strictly needed but a very good optimization to avoid replacing the full object
        // NOTE: it is now used with UpdateType for guidobject collections
        public bool CommitUpdateItem(string fieldname, object item_index_or_key, UpdateType update_type = UpdateType.SetField)
        {
            // retrieve field value with reflection 
            var f = GetType().GetField(fieldname, BindingFlags.Public | BindingFlags.Instance);
            if (f == null)
            {
                Logger.DefaultLog.WriteLine($"ScheduleUpdate: field {fieldname} not found (when setting item)");
                return false;
            }

            bool is_remove = (update_type == UpdateType.RemoveItem);

            // Check which kind of collection
            try
            {

                if (f.FieldType.IsArray)
                {
                   SharedObjectMap.AddUpdate(GuidUpdate.ToClient(
                       __guid,
                        fieldname,
                        f.FieldType.GetElementType(), 
                        is_remove ? null : CloneWithJson((f.GetValue(this) as Array).GetValue((int) item_index_or_key)),
                        item_index_or_key, update_type
                        ));
                }
                else if (typeof(IList).IsAssignableFrom(f.FieldType))
                {
                    SharedObjectMap.AddUpdate(GuidUpdate.ToClient(
                        __guid,
                        fieldname,
                        f.FieldType.GenericTypeArguments[0],
                         is_remove ? null : CloneWithJson((f.GetValue(this) as IList))[(int)item_index_or_key],
                        item_index_or_key, update_type
                        ));
                }
                else if (typeof(IDictionary).IsAssignableFrom(f.FieldType))
                {
                    SharedObjectMap.AddUpdate(GuidUpdate.ToClient(
                        __guid,
                        fieldname,
                        f.FieldType.GetGenericArguments()[1],
                         is_remove ? null : CloneWithJson((f.GetValue(this) as IDictionary))[item_index_or_key],
                        item_index_or_key, update_type
                        ));
                }
                else
                {
                    // ERROR
                    if (SharedObjectMap.Debug)
                        SharedObjectMap.log($"CommitUpdateItem: field {fieldname} cannot set an update item");
                    return false;
                }
            }
            catch(Exception ex)
            {
                Logger.DefaultLog.logException(SharedObjectMap.log($"CommitUpdateItem: field {fieldname} exception during CommitUpdateItem()"),ex);
                return false;
            }            

            return true;
        }


        // will schedule an update of requested fields
        public bool CommitUpdate(List<string> fields)
        {
            // retrieve field value with reflection 
            foreach (var fieldname in fields)
            {
                var f = GetType().GetField(fieldname, BindingFlags.Public | BindingFlags.Instance);
                if (f == null)
                {
                    if (SharedObjectMap.Debug)
                        SharedObjectMap.log($"CommitUpdate: field {fieldname} not found");

                    return false;
                }

                SharedObjectMap.AddUpdate(GuidUpdate.ToClient(__guid, fieldname, f.FieldType, CloneWithJson(f.GetValue(this))));
            }

            return true;
        }

        public override string ToString()
        {
            return $"GuidObject({__guid}) {SharedObjectMap.FindGuidObjectPathAsString(__guid)}";
        }

        public void log(string str)
        {
            SharedObjectMap.log(this, str);
        }       

        // CHECK OF CONSISTENCY

        // This method will be called after any updates (single fields, or whole object)
        // It is the class responsibility to send back updated values with commits
        // During server updates update is valorized and contains all the transaction information on the changed field and its previous value
        public virtual void NotifyModelUpdate(GuidUpdate update = null)
        {
            // mimics the view NotifyUpdate

            GuidObject m = this;
            while (m != null)
            {
                if (m.onNotifyUpdate != null)
                {
                    if (m.onNotifyUpdate.Invoke(update))
                        break;
                }

                m = m.__Parent;
            }

            // global handler
            SharedObjectMap.notifyModelUpdate?.Invoke(update);         
        }

        // Clone produces a "clean" object, needs to be added
		public T Clone<T>() where T : GuidObject
		{
            // Clones the whole object using Json serialization, then clears the guids
            var json_clone = JsonConvert.SerializeObject(this);
            var obj_clone = JsonConvert.DeserializeObject<T>(json_clone);

            SharedObjectMap._scanGuidObjects(obj_clone, new Operation() { OperationType = OperationType.CleanGuids });

            return obj_clone;            
		}


		// FIXME: SUPER-DANGEROUS!
		/*
        ~GuidObject()
        {

            RemoveFromSharedObjectMap();

            
            if (SharedObjectMap.Debug)
                SharedObjectMap.log(this, "DESTROYED");
                
        }
        */

		// Helpers


	}

    // Result of update operation
    [Obfuscation_Skip]
	[System.Reflection.ObfuscationAttribute(Exclude=true)]
    public struct SingleUpdateResult
    {
        public UpdateResultFlags Flags;
        public object ChangedValue;  // if ValueChanged, this is the server-provided value

        public object GetFinalValue(object original_value)
        {
            if (Flags.HasFlag(UpdateResultFlags.ValueChanged))
                return SharedObjectMap.ConvertFromJson(ChangedValue, original_value.GetType());
            else
                return original_value;
        }

        public bool IsValid => (Flags.HasFlag(UpdateResultFlags.Updated) || Flags.HasFlag(UpdateResultFlags.InCommit) || Flags.HasFlag(UpdateResultFlags.NullGuid)) && !Flags.HasFlag(UpdateResultFlags.ErrorGeneric);

        public bool IsDelayed => (Flags.HasFlag(UpdateResultFlags.InCommit));

        public readonly static SingleUpdateResult OK = new SingleUpdateResult() { Flags = UpdateResultFlags.Updated };
        public readonly static SingleUpdateResult ResultGenericError = new SingleUpdateResult() { Flags = UpdateResultFlags.ErrorGeneric };
        public readonly static SingleUpdateResult InCommit = new SingleUpdateResult() { Flags = UpdateResultFlags.InCommit };
        public readonly static SingleUpdateResult Skip = new SingleUpdateResult() { Flags = UpdateResultFlags.NullGuid };
		public readonly static SingleUpdateResult ObjectNotFound = new SingleUpdateResult() { Flags = UpdateResultFlags.ObjectNotFound };
	}


    // Flags for SingleUpdateResult

    [Flags]
    [Obfuscation_Skip]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public enum UpdateResultFlags
    {
        None = 0,
        Updated = 1 << 0,            // the field has been updated with the provided value
        ValueChanged = 1 << 1,       // the field has been updated, but its value was changed by the server
        ObjectNotFound = 1 << 2,     // object was not in map
        FieldNotFound = 1 << 3,      // field was not found
        ErrorSettingValue = 1 << 4, // error during the reflection set
        ErrorGeneric = 1 << 5,      // other errors (also in higher levels)

        InCommit=512,               // return during a commit batch
        NullGuid=2014               // no-op because update guid was -1
    }

    // Flags sent with update
    [Flags]
    [Obfuscation_Skip]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public enum UpdateFlags
    {
        None = 0,
        NeedResult = 1 << 0,             // this update was called to get a result
        DoNotNotify = 1 << 1,            // hint not to notify views, for performance
        DirectSync = 1 << 2,             // hint to use quickest protocol (in ClientServer this is an override for PreferOSC)
        NoDirectSync = 1 << 3            // hints not to use quickest protocol (ex: large payloads)
    }

    [Obfuscation_Skip]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public enum UpdateType
    {
        SetField = 1,           // standard set field
        
        AddItem  = 2,           // adds an item (server side)
        RemoveItem = 3,         // removes an item (server side)

        ClearItems = 4,         // clears collection

        CallMethod = 10
    }

    // Update structure used for both the the model on the server, or the view on the client
    [Obfuscation_Skip]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class GuidUpdate
    {
        public long Guid = -1;              // guid of the object to be updated

        public long Seq = -1;               // generated sequence of updates for debugging server/client
                                            // these are generated by both the server and the client on individual sequences
        
        public UpdateType UpdateType = UpdateType.SetField;       // the type of the update
        public UpdateFlags Flags = UpdateFlags.None;              // flags sent with the update

        public string FieldName;
        public string FieldType;                       // this is actually the item type if we are updating a collection item with an ItemIndexOrKey set
        public object FieldValue;

        public object PreviousValue;        // this will be valorized during the UpdateField to allow Validate to intercept certain changes

        public object ItemIndexOrKey;

        public bool IsFromServer = false;

        public bool IsDirectSync = false;   // if directsync will hint the Sync to use a faster method

        // Method call
        public string   MethodName;
        public object[] MethodArguments;
        public string[] MethodTypes;            // System.Type converted to strings

        // Valorized during reception
        [JsonIgnore]
        public IPEndPoint endpoint;

        // update sent from client to server
        // New sequence in generated only in the first call and remains constant accross network calls
        public static GuidUpdate ToServer(long guid, string name, Type objtype, object value, object itemindexorkey = null, UpdateType update_type = UpdateType.SetField)
        {
            var u = new GuidUpdate(guid, name, objtype, value, itemindexorkey) { IsFromServer = false, UpdateType = update_type };
            u.Seq = SharedObjectMap.GetNextUpdateSequence();
            return u;
        }

        // update sent from server to client
        // New sequence in generated only in the first call and remains constant accross network calls
        public static GuidUpdate ToClient(long guid, string name, Type objtype, object value, object itemindexorkey = null, UpdateType update_type = UpdateType.SetField)
        {
            var u = new GuidUpdate(guid, name, objtype, value, itemindexorkey) { IsFromServer = true, UpdateType = update_type };
            u.Seq = SharedObjectMap.GetNextUpdateSequence();
            return u;
        }

        public static GuidUpdate ToServerCall(long guid, string method_name, object[] method_arguments, string[] method_types, UpdateType update_type = UpdateType.SetField, UpdateFlags flags = UpdateFlags.None)
        {
            var u = new GuidUpdate(guid, method_name, method_arguments, method_types) { IsFromServer = false, UpdateType = UpdateType.CallMethod, Flags = flags };
            u.Seq = SharedObjectMap.GetNextUpdateSequence();
            return u;
        }

        public GuidUpdate()
        { }

        // normal update
        public GuidUpdate(long guid, string name, Type objtype, object value, object itemindexorkey = null)
        {
            Guid = guid;
            FieldName = name;
            FieldType = objtype?.ToString();
            FieldValue = value;

            ItemIndexOrKey = itemindexorkey;            
        }

        // method call
        public GuidUpdate(long guid, string method_name, object[] method_arguments, string[] method_types)
        {
            Guid = guid;
            MethodName = method_name;
            MethodArguments = method_arguments;
            MethodTypes = method_types;
        }

        // NOTE: Path is very useful to detect changes in collections etc.. but computing it is expensive.
        // So GuidObject's __path property cachees the path up to the GuidObject, and GuidUpdate always append fieldname and item key
        private string __path;
        public string Path
        {
            get
            {
                if (string.IsNullOrEmpty(__path))
                {
                    // if (TryGetObject(Guid, out GuidObject go))
                    // NOTE: locking was stalling 
                    if (object_map.TryGetValue(Guid, out GuidObject go))
                        __path = $"{go.__path}{((Guid != SharedObjectMap.RootModel.__guid && !string.IsNullOrEmpty(FieldName)) ? "." : "")}{FieldName}{(ItemIndexOrKey != null ? $"[{ItemIndexOrKey}]" : "")}";
                    else
                    {
                        // this is an error
                        __path = $"<missing {Guid}>";
                        //throw new ArgumentException("Cannot calculate path for GuidUpdate");
                    }
                }

                return __path;
            }
        }

        // Returns the last part of the property path for matching
        private string __path_end;
        public string PathEnd
        {
            get
            {
				if (string.IsNullOrEmpty(__path_end))
				{
					var match = Regex.Match(Path, @"\.(\w+)$");

                    if (match.Success)
                        __path_end = match.Groups[1].Value;
                    else
                        return string.Empty;
				}

				return __path_end;
			}
        }


        public override string ToString()
        {
            if (!string.IsNullOrEmpty(MethodName))
            {
                var args = MethodArguments != null ? string.Join(",", (from m in MethodArguments select (m==null ? "null" :  m.ToString()))) : "";
                return $"GuidUpdate({Guid},#{Seq}) {(SharedObjectMap.IsServer == IsFromServer ? "=>" : "<=")} CALL {Path}.{MethodName}({args})";              // TODO: return type?
            }
            else
                return $"GuidUpdate({Guid},#{Seq}) {(SharedObjectMap.IsServer == IsFromServer ? "=>" : "<=")} {Path} = {FieldValue} ({FieldType})";
        }

        public string ToStringMethodCall(object obj_ref, Type result_type, object result)
        {
            bool has_result = result_type != typeof(void);

            return $"GuidUpdate({Guid},#{Seq}) {(SharedObjectMap.IsServer == IsFromServer ? "=>" : "<=")} CALL ({obj_ref.GetType()}) {Path}.{MethodName}(){(has_result ? $" => ({result_type}) {result}" : "")}";
        }

        public void log(string str)
        {
            SharedObjectMap.log(this, str);
        }

        #region Match

        // Contains the string in the whole path (useful for GuidObject classes like Vector2f etc..)
        public bool Contains(string s) => Path.Contains(s);

        // Matches EXACTLY the last property in the path (useful for integral types)
        public bool Matches(string s) => PathEnd.Equals(s);

		#endregion
	}

	[Obfuscation_Skip]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public struct ServerUpdateResult
    {
        public long UpdateSequence;
        public GuidUpdate[] Updates;
    }


	// Views

	// GuidObject sync view, also used for lists, arrays, dictionaries base classes
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public interface IGuidObjectSyncView
    {
        void OnItemUpdated(bool from_server, object item_index_or_key, UpdateType update_type);                                 // For collections of non-guidobjects
        void OnItemUpdated(bool from_server, string collection_fieldname, object item_index_or_key, UpdateType update_type);    // For collections of guidobjects

        IGuidObjectSyncView     GetParent();
        GuidObject              GetModel();
        void                    SetModel(object _model, IGuidObjectSyncView _parent = null, object _container = null);
        object                  GetContainerIndexOrKey();

        void CommitBegin();
        void CommitEnd();

        // Methods for syncing, defined in the rootview
        // Direct methods are called for DirectSync attributed fields
        Func<GuidUpdate, bool, SingleUpdateResult>                  onViewSync { get; set; }
        Func<List<GuidUpdate>, bool, SingleUpdateResult[]>          onViewSyncBatch { get; set; }

        // DEPRECATED: everything goes Sync() with the GuidUpdate flag DirecSync
        /*
        Action<long,string,int, int>                                onViewSyncDirectInt { get; set; }
        Action<long, string, float, int>                            onViewSyncDirectFloat { get; set; }
        Action<long, string, double, int>                           onViewSyncDirectDouble { get; set; }
        */

        Func<bool, IGuidObjectSyncView, string, object,UpdateType,bool>        onNotifyUpdate { get; set; }
            // from_server, view (original), fieldname, item_index_or_key, update_type

        void NotifyViewUpdate(bool from_server, IGuidObjectSyncView view, string fieldname, object item_index_or_key = null, UpdateType update_type = UpdateType.SetField);



	}


	// Wrappers (server-side auto-update)
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public interface IWrapper
    {
        GuidObject GetModel();
        void SetModel(object model);
    }

	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public interface IWrapperCollection
    {
        void Rebuild();
    }

    // The base class for server-side wrappers
    [Obfuscation_Skip]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class WrapperBase<M> : IWrapper where M : GuidObject 
    {
        public M _Model { get; private set; }

        public WrapperBase(M m)
        {
            SetModel(m);
        }

        public void SetModel(object model)
        {
            SetModel((M) model);
        }

        public void SetModel(M model)
        {
            _Model = model;
            if (_Model!=null)
                _Model.__model_wrapper = this;
        }

        public GuidObject GetModel() => _Model;

        // Commit methods are virtual so wrappers can implement custom pre/after logic

        public virtual void CommitBegin() => CommitBegin_Wrapper(_Model);

        public virtual void CommitEnd() => CommitEnd_Wrapper(_Model);
        /*
        public async virtual Task<bool> CommitEndAndWait(int timeout=5000, int delay=16)
        {
            return await SharedObjectMap.CommitEndAndWait(_Model, timeout, delay);
        }
        */

        public virtual void CommitUpdate()
        {
            _Model.CommitUpdate();
        }

        // Selective commit cannot be overridden

        public void CommitUpdate(string field) => _Model.CommitUpdate(field);

        public void CommitUpdate(List<string> fields) => _Model.CommitUpdate(fields);

        public bool InCommit => SharedObjectMap.IsCommitDelayed(_Model);
    }

    // M: is the real model class
    // W: is the model's wrapper class
    [Obfuscation_Skip]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class WrapperGuidObjectList<M, W> : IWrapperCollection, IList<W> where W : WrapperBase<M>, new() where M : GuidObject
    {
        // reference to the real model list or array        
        private List<M> _list;
        private M[] _array;

        private IList _mdata => (_list != null ? (IList)_list : (IList)_array);

        // internal list of wrapper classes (private backing store)
        private List<W> _wlist;
        private GuidObject _parent;
        private string _fieldname;

        public WrapperGuidObjectList(List<M> list, GuidObject parent, string fieldname)
        {
            _list = list;
            _parent = parent;
            _fieldname = fieldname;

            _rebuild_internal_list();
        }

        public WrapperGuidObjectList(M[] array, GuidObject parent, string fieldname)
        {
            _array = array;
            _parent = parent;
            _fieldname = fieldname;

            _rebuild_internal_list();
        }

        public void Rebuild() => _rebuild_internal_list();

        // refreshes the internal wrapper's list
        private void _rebuild_internal_list()
        {
            _wlist = new List<W>();

            foreach (var i in _mdata)
            {
                W w = new W();
                w.SetModel((M)i);
                _wlist.Add(w);
            }
        }

        private void _update_field()
        {
            _parent?.CommitUpdate(_fieldname);
        }

        private void _update_item(int index, UpdateType update_type = UpdateType.SetField)
        {
            _parent?.CommitUpdateItem(_fieldname, index, update_type);
        }

        public W this[int index]
        {
            get => _wlist[index];

            set
            {
                ReplaceAt(index, value);
            }
        }

        public int Count => _wlist.Count;

        public bool IsReadOnly => false; // ?

        // forwards using item's model, just for completeness
        public void Add(W item)
        {
            Add(item._Model);
        }

        // works on model
        public void Clear()
        {
            lock (this)
            {
                if (_array != null)
                {
                    foreach (var i in _array)
                        SharedObjectMap.RemoveAllGuidObjects(i);
                    Array.Clear(_array, 0, _array.Length);
                }
                else if (_list != null)
                {
                    foreach (var i in _list)
                        SharedObjectMap.RemoveAllGuidObjects(i);
                    _list?.Clear();
                }
                else
                    throw new NotSupportedException();

               
                _rebuild_internal_list();
                _update_field();
            }
        }

        public bool Contains(W item)
        {
            return _wlist.Contains(item);
        }

        public void CopyTo(W[] array, int arrayIndex)
        {
            _wlist.CopyTo(array, arrayIndex);
        }

        public IEnumerator<W> GetEnumerator()
        {
            // TEST: will it cast?
            return (IEnumerator<W>)_wlist.GetEnumerator();
        }

        public int IndexOf(W item)
        {
            return _wlist.IndexOf(item);
        }

        // forwards using item's model, not useful
        public void Insert(int index, W item)
        {
            Insert(index, item._Model);
        }

        // forwards using item's model
        public bool Remove(W item)
        {
            return Remove(item._Model);
        }

        public void RemoveAt(int index)
        {
            lock (this)
            {
                SharedObjectMap.RemoveAllGuidObjects(_mdata[index]);

                if (_array != null)
                {
                    _array[index] = default;
                }
                else
				{
                    _mdata.RemoveAt(index);               
                }

                _update_item(index, UpdateType.RemoveItem);
                _rebuild_internal_list();
            }
        }        

        IEnumerator IEnumerable.GetEnumerator()
        {
            throw new NotSupportedException();
            //return _wlist.GetEnumerator();
        }

        // real modification methods based on model's item class

        public void Add(M item)
        {
            if (_array != null)
                throw new NotSupportedException();

            lock (this)
            {
                item.__guid_parent = _parent.__guid;

                AddAllGuidObjects(item);

                _list?.Add(item);
                _rebuild_internal_list();
                _update_item(_list.IndexOf(item), UpdateType.AddItem);
            }
        }

        public void Insert(int index, M item)
        {
            lock (this)
            {
                item.__guid_parent = _parent.__guid;

                AddAllGuidObjects(item);

                if (_array != null)
                {
                    _array[index] = item;
                }
                else
				{
                    _list?.Insert(index, item);            
                }

                _update_item(index, UpdateType.AddItem);
                _rebuild_internal_list();
            }
        }

        public int IndexOf(M item)
        {
            return _mdata.IndexOf(item);
        }

        public bool Remove(M item)
        {
            bool ret = false;

            lock (this)
            {
                int index = -1;
                if (_list != null)
                {
                    index = _list.IndexOf(item);
                    ret = _list.Remove(item);
                    SharedObjectMap.RemoveAllGuidObjects(item);
                }
                else
                    throw new NotSupportedException("Remove not supported for arrays");

                _rebuild_internal_list();
                //_update_field();
                _update_item(index, UpdateType.RemoveItem);  // TEST
            }

            return ret;
        }

        public bool Contains(M item)
        {
            return _mdata.Contains(item);
        }

        public void CopyTo(M[] array, int arrayIndex)
        {
            _mdata.CopyTo(array, arrayIndex);
        }

        //OLD: merged RemoveAt and Insert to avoid two updates
        //NOW: use a remove and add update (2 updates)
        public void ReplaceAt(int index, W item)
        {
            lock (this)
            {
                RemoveAt(index);
                Insert(index, item);
            }
        }

        // Commit methods are virtual so wrappers can implement custom pre/after logic

        public virtual void CommitUpdate()
        {
            _update_field();
        }
    }

    // M: is the real model class
    [Obfuscation_Skip]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class WrapperList<M> : IWrapperCollection, IList<M>
    {
        // reference to the real model list or array        
        private List<M> _list;
        private M[] _array;

        private IList _mdata => (_list != null ? (IList)_list : (IList)_array);

        private GuidObject _parent;
        private string _fieldname;

        public WrapperList(List<M> list, GuidObject parent, string fieldname)
        {
            _list = list;
            _parent = parent;
            _fieldname = fieldname;
        }

        public WrapperList(M[] array, GuidObject parent, string fieldname)
        {
            _array = array;
            _parent = parent;
            _fieldname = fieldname;
        }

        public void Rebuild()
        {}

        private void _update_field()
        {
            _parent?.CommitUpdate(_fieldname);
        }

        private void _update_item(int index)
        {
            _parent?.CommitUpdateItem(_fieldname, index);
        }

        public M this[int index]
        {
            get => (M)_mdata[index];

            set
            {
                lock (this)
                {
                    _mdata[index] = value;
                    _update_item(index);
                }
            }
        }

        public int Count => _mdata.Count;

        public bool IsReadOnly => false; // ?

        public void Add(M item)
        {
            if (_array != null)
                throw new NotSupportedException("array cannot Add()");

            lock (this)
            {
                _mdata.Add(item);
                _update_field();
            }
        }

        public void Clear()
        {
            lock (this)
            {
                _mdata.Clear();
                _update_field();
            }
        }

        public bool Contains(M item) => _mdata.Contains(item);

        public void CopyTo(M[] array, int arrayIndex)
        {
            _mdata.CopyTo(array, arrayIndex);
        }

        //TEST
        public IEnumerator<M> GetEnumerator() => (IEnumerator<M>)_mdata.GetEnumerator();

        public int IndexOf(M item) => _mdata.IndexOf(item);

        public void Insert(int index, M item) => _mdata.Insert(index, item);

        public bool Remove(M item)
        {
            if (_array != null)
                throw new NotSupportedException("array cannot Remove()");

            lock (this)
            {
                bool ret = _mdata.Contains(item);
                _mdata.Remove(item);
                _update_field();

                return ret;
            }
        }

        public void RemoveAt(int index)
        {
            if (_array != null)
                throw new NotSupportedException("array cannot RemoveAt()");

            lock (this)
            {
                _mdata.RemoveAt(index);
                _update_field();
            }
        }

        IEnumerator IEnumerable.GetEnumerator() => _mdata.GetEnumerator();

        // Commit methods are virtual so wrappers can implement custom pre/after logic

        public virtual void CommitUpdate()
        {
            _update_field();
        }

        public virtual void CommitUpdate(int index)
        {
            _update_item(index);
        }


    }

    // MK: is the real model key class
    // MV: is the real model value class
    // W: is the model's wrapper class
    [Obfuscation_Skip]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class WrapperGuidObjectDictionary<MK, MV, W> : IWrapperCollection, IDictionary<MK, W> where W : WrapperBase<MV>, new() where MV : GuidObject
    {
        // reference to the real model dictionary       
        private IDictionary<MK, MV> _dict;

        // internal dict of wrapper classes (private backing store)
        private IDictionary<MK, W> _wdict;
        private GuidObject _parent;
        private string _fieldname;

        public WrapperGuidObjectDictionary(IDictionary<MK, MV> dict, GuidObject parent, string fieldname)
        {
            _dict = dict;
            _parent = parent;
            _fieldname = fieldname;

            _rebuild_internal_dict();
        }

        public void Rebuild() => _rebuild_internal_dict();

        private void _update_field()
        {
            _parent?.CommitUpdate(_fieldname);
        }

        private void _update_item(object key, UpdateType update_type = UpdateType.SetField)
        {
            _parent?.CommitUpdateItem(_fieldname, key, update_type);
        }

        // refreshes the internal wrapper's dictionary
        private void _rebuild_internal_dict()
        {
            _wdict = new Dictionary<MK, W>();

            foreach (var kvp in _dict)
            {
                W w = new W();
                w.SetModel(kvp.Value);

                _wdict.Add(kvp.Key, w);
            }
        }

        // forwards to wrapper's model
        public W this[MK key]
        {
            get => _wdict[key];
            set => ReplaceAt(key, value);
        }

        public ICollection<MK> Keys => _wdict.Keys;

        public ICollection<W> Values => _wdict.Values;

        public int Count => _dict.Count;

        public bool IsReadOnly => false;

        public void Add(MK key, W value)
        {
            lock (this)
            {
                value._Model.__guid_parent = _parent.__guid;

                AddAllGuidObjects(value._Model);

                _dict.Add(key, value._Model);
                _rebuild_internal_dict();
                //_update_field();
                _update_item(key, UpdateType.AddItem);
            }
        }

        public void Add(KeyValuePair<MK, W> item)
        {
            lock (this)
            {
                item.Value._Model.__guid_parent = _parent.__guid;

                AddAllGuidObjects(item.Value._Model);

                _dict.Add(new KeyValuePair<MK, MV>(item.Key, item.Value._Model));
                _rebuild_internal_dict();
                //_update_field();
                _update_item(item.Key, UpdateType.AddItem);
            }
        }

        public void Clear()
        {
            lock (this)
            {
                foreach (var kvp in _dict)
                    SharedObjectMap.RemoveAllGuidObjects(kvp.Value);

                _dict.Clear();
                _rebuild_internal_dict();
                _update_field();
            }
        }

        public bool Contains(KeyValuePair<MK, W> item) => _wdict.Contains(item);

        public bool ContainsKey(MK key) => _wdict.ContainsKey(key);

        public void CopyTo(KeyValuePair<MK, W>[] array, int arrayIndex) => _wdict.CopyTo(array, arrayIndex);

        public IEnumerator<KeyValuePair<MK, W>> GetEnumerator() => _wdict.GetEnumerator();

        public bool Remove(MK key)
        {
            lock (this)
            {
                if (_dict.ContainsKey(key))
                    SharedObjectMap.RemoveAllGuidObjects(_dict[key]);

                var ret = _dict.Remove(key);
                _rebuild_internal_dict();
                //_update_field();
                _update_item(key, UpdateType.RemoveItem);

                return ret;
            }
        }

        public bool Remove(KeyValuePair<MK, W> item)
        {
            lock (this)
            {
                SharedObjectMap.RemoveAllGuidObjects(item.Value._Model);

                var ret = _wdict.Remove(item);
                _rebuild_internal_dict();
                //_update_field();
                _update_item(item.Key, UpdateType.RemoveItem);

                return ret;
            }
        }

        public bool TryGetValue(MK key, out W value) => _wdict.TryGetValue(key, out value);

        IEnumerator IEnumerable.GetEnumerator() => _dict.GetEnumerator();

        
        public void ReplaceAt(MK key, W item)
        {
            lock (this)
            {
                Remove(key);
                Add(key, item);
            }
        }

        // Commit methods are virtual so wrappers can implement custom pre/after logic

        public virtual void CommitUpdate()
        {
            _update_field();
        }     
    }

    // MK: is the real model key class
    // MV: is the real model value class
    [Obfuscation_Skip]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class WrapperDictionary<MK, MV> : IWrapperCollection, IDictionary<MK, MV>
    {
        // reference to the real model dictionary       
        private IDictionary<MK, MV> _dict;

        private GuidObject _parent;
        private string _fieldname;

        public WrapperDictionary(IDictionary<MK, MV> dict, GuidObject parent, string fieldname)
        {
            _dict = dict;
            _parent = parent;
            _fieldname = fieldname;
        }

        public void Rebuild()
        { }

        private void _update_field()
        {
            _parent?.CommitUpdate(_fieldname);
        }

        private void _update_key(MK key)
        {
            _parent?.CommitUpdateItem(_fieldname, key);
        }

        public MV this[MK key]
        {
            get => _dict[key];
            set
            {
                lock (this)
                {
                    _dict[key] = value;
                    _update_key(key);
                }
            }
        }

        public ICollection<MK> Keys => _dict.Keys;

        public ICollection<MV> Values => _dict.Values;

        public int Count => _dict.Count;

        public bool IsReadOnly => false;

        public void Add(MK key, MV value)
        {
            lock (this)
            {
                _dict.Add(key, value);
                _update_field();
            }
        }

        public void Add(KeyValuePair<MK, MV> item)
        {
            lock (this)
            {
                _dict.Add(item);
                _update_field();
            }
        }

        public void Clear()
        {
            lock (this)
            {
                _dict.Clear();
                _update_field();
            }
        }

        public bool Contains(KeyValuePair<MK, MV> item) => _dict.Contains(item);

        public bool ContainsKey(MK key) => _dict.ContainsKey(key);

        public void CopyTo(KeyValuePair<MK, MV>[] array, int arrayIndex) => _dict.CopyTo(array, arrayIndex);

        public IEnumerator<KeyValuePair<MK, MV>> GetEnumerator() => _dict.GetEnumerator();

        public bool Remove(MK key)
        {
            lock (this)
            {
                var ret = _dict.Remove(key);
                _update_field();

                return ret;
            }
        }

        public bool Remove(KeyValuePair<MK, MV> item)
        {
            lock (this)
            {
                var ret = _dict.Remove(item);
                _update_field();

                return ret;
            }
        }

        public bool TryGetValue(MK key, out MV value) => _dict.TryGetValue(key, out value);

        IEnumerator IEnumerable.GetEnumerator() => _dict.GetEnumerator();

        // Commit methods are virtual so wrappers can implement custom pre/after logic

        public virtual void CommitUpdate()
        {
            _update_field();
        }

        public virtual void CommitUpdate(MK key)
        {
            _update_key(key);
        }
    }
}
 
 