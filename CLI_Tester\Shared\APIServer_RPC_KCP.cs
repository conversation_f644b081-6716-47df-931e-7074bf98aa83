﻿//TABULA_GUID:{2FD66F0A-B85C-4311-8931-9CCA55CF41DE}

using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Linq;
using Tabula.RPC;

using static Tabula.RPC.Server;
using System.Net;
using Tabula.API;

/**
 *  APIServer_RPC
 *  
 *  Creates a standard wiring between a Tabula.RPC.Server (based on Json rpc), and an existing APIWrapper
 * 
 *  Usage: 
 *      var server = new APIServer_RPC(port, server_api_wrapper);     
 * 
 * 
 *  Dependencies:
 *      APIWrapper, TabulaJsonRpc
 *      
 *      
 *  Changelog:
 *      1.4 (09/05/2020) GenerateContract() has ContractOptions to also suppress class inclusion or exclude classes/namespaces
 *      1.3 (11/04/2020) GenerateContract() moved here from APIWrapper, OSC generation is now in APIServer_OSC using partial class for the client
 *      1.2 synched with FacadeSignage RPC.cs, batch call is now handled at APIWrapper level
 *      1.1 (18/12/2017): removed Verbose, if onLog and onLogException are defined they will be called, optional next-port-retry, using RPCResult with error with wrong param number or bad type
 *      1.0 stable
 */

namespace Tabula.APIServer
{
    // Base class for special attributes
    public class APIMethodAttribute : Attribute
	{
        public bool GenerateNoResult = false;   // will also generate a _NoResult stub based on the same call
    }


    public class APIServer_RPC
    {
        public static bool Debug = false;

        public Action<bool, int>                                            onServerStarted;        // <start state>, <connected_port>
        public Action<long, IPEndPoint>                                     onClientConnected;      //KCP_PORTING: made the arg an IPEndPoint
		public Action<long>                                                 onClientDisconnected;
        public Action<string>                                               onLog;
        public Action<string, Exception>                                    onLogException;

        public bool         TryNextPort = false;    // if true will try next ports if first is not available

        public int          Port = 33135;

        private APIWrapper  api;
        public RPC.Server   Server;                 // KCP_PORTING: made public in order to use events to process early messages

        public APIServer_RPC(int port, APIWrapper wrappper)
        {
            api = wrappper;
            Port = port;
        }

        public bool Start()
        {
            if (Server != null)
                return true;

            Server = new Server();
            Server.OnClientConnected += (connection_id, remote_endpoint) =>
            {
                if (Debug)
                    onLog?.Invoke($"APIServer_RPC: RPC connection_id={connection_id} connected ({remote_endpoint.Address}:{remote_endpoint.Port})");

                onClientConnected?.Invoke(connection_id, remote_endpoint);
            };

            Server.OnClientDisconnected += (connection_id, remote_endpoint) =>
            {
                if (Debug)
                    onLog?.Invoke($"APIServer_RPC: connection_id={connection_id} disconnected ({remote_endpoint.Address}:{remote_endpoint.Port})");

                onClientDisconnected?.Invoke(connection_id);
            };

            Server.OnProcessCall += ProcessCall;

            Server.OnServerException += (ex) =>
            {
                onLogException?.Invoke("APIServer_RPC: ServerException", ex);
            };

            // Tries to start the server, if it fails increment by 10 for a maximum of 3 times
            var rpc_ret = Server.StartServer(Port, (TryNextPort ? 3 : 1)).Result;

            onServerStarted?.Invoke(rpc_ret.Item1, rpc_ret.Item2);

            if (rpc_ret.Item1 == true)
            {
                Port = rpc_ret.Item2;
                onLog?.Invoke("APIServer_RPC: started on port: " + Port);                               
                return true;
            }
            else
            {
                onLog?.Invoke("APIServer_RPC: Could not start on port " + Port);
                return false;
            }
        }

        public void Stop()
        {
            Server?.StopServer().Wait(3000);
            Server = null;
        }
        
        public RPCResult ProcessCall(RPCCall call)
        {            
            if (Debug)
                onLog?.Invoke("RPC Call: " + call.ToString());

            object      apireturn;
            RPCResult   result;
            IPEndPoint  ip_ep = call.RemoteEndPoint;

            // Check if it is a batch call
            if (call.Batch?.Length>0)
            {                                                
                try
                {
                    APIWrapper.Batch batch = new APIWrapper.Batch();

                    foreach (var bc in call.Batch)
                    {
                        batch.methods.Add(bc.NameId);
                        batch.arguments.Add(bc.Arguments);
                    }

                    apireturn = api.Call(ip_ep, batch);                           
                }
                catch (Exception ex)
                {
                    onLogException?.Invoke($"APIServer_RPC: API Error Batch", ex);

                    return RPCResult.CreateError(call, "error in batch");    // TODO: clean!!!
                }

                return RPCResult.Create(call, apireturn);             
            }

            // Single call

            try
            {                                                
                apireturn = api.Call(call.NameId, ip_ep, call.Arguments);
            }
            catch (FormatException ex)
            {
                // Error casting parameters
                onLogException?.Invoke($"APIServer_RPC: API Error {(call.NameId != 0 ? "id:" + call.NameId.ToString() : call.Name)} error in parameter type", ex);
                return RPCResult.CreateError(call, "bad parameter type");
            }
            catch (System.Reflection.TargetParameterCountException ex)
            {
                // Wrong number of parameters
                onLogException?.Invoke($"APIServer_RPC: API Error {(call.NameId != 0 ? "id:" + call.NameId.ToString() : call.Name)} wrong parameter count", ex);
                return RPCResult.CreateError(call, "wrong parameter count");
            }
            catch (Exception ex)
            {
                onLogException?.Invoke($"APIServer_RPC: API Error {(call.NameId != 0 ? "id:" + call.NameId.ToString() : call.Name)}", ex);
                return RPCResult.CreateError(call, "generic error");
            }

            // generic call
            result = RPCResult.Create(call, apireturn);
            
            return result;            
        }

		#region Contract

		public class ContractOptions
        {
            public APIWrapper   ApiWrapper;
            public string       FileGuid;
            public string       NameSpace;
            public string       ClientClassName;
            public List<Type>   ClientInterfaces; // interfaces the client must implement

            public bool             IncludeClasses = true;
            public bool             GenerateCallEnum = false;   // generates an enum with all the calls (_api_methods)
            public List<Type>       excluded_classes = null;
            public List<string>     excluded_namespaces = null;

            public string       OutputFile;
        }


        // Generate a custom class for a Tabula.RPC.ManagedClient
        // Moved from APIWrapper.cs because it is related to TCP/RPC
        public static void GenerateContract(ContractOptions opts)
        {
            string tabulaguid = !string.IsNullOrEmpty(opts.FileGuid) ? $"//TABULA_GUID:{opts.FileGuid}" : "";

            // Mark the generation with a timestamp

            string s_usings =
$@"{tabulaguid}
// Generated {opts.ClientClassName} API inheriting from Tabula.RPC.ManagedClient (include TabulaJsonRPC.cs in project)
// DateTime: {DateTime.Now.ToLongDateString()}

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Tabula;
using Tabula.RPC;
";

            string s_namespace_start =
$@"namespace {opts.NameSpace} {{
";

            string s_namespace_end = "}";

            string s_interfaces = "";
            if (opts.ClientInterfaces != null && opts.ClientInterfaces.Count>0)
            {
                s_interfaces += ", " + String.Join(", ", opts.ClientInterfaces.Select(x => x.ToString()).ToArray());             
            }


            string s_class_start =
$@"
[System.Reflection.ObfuscationAttribute(Exclude=true)]
public partial class {opts.ClientClassName} : ManagedClient{s_interfaces}
{{
    public {opts.ClientClassName}() {{}}

    public {opts.ClientClassName}(string address, int port) : base(address, port) {{}}

";

            string s_class_end =
@"}";

            StringBuilder sb_functions = new StringBuilder(),
                            sb_functions_enum = new StringBuilder();

            if (opts.GenerateCallEnum)
            {
                sb_functions_enum.Append(
	@"[System.Reflection.ObfuscationAttribute(Exclude = true)]
    public enum _api_methods {
    None = 0");
            }
            

            List<Type> custom_classes = new List<Type>();

            foreach (var kvp in opts.ApiWrapper.MethodCache)
            {
				APIWrapper.CachedMethod cm = kvp.Value;

                // adds method, NOTE: same method with different parameters will result in a conflict
                if (opts.GenerateCallEnum)
                    sb_functions_enum.Append($",\n\t{cm.method} = {cm.method_id}");

                try
                {
                    // builds the arguments string
                    var args = new List<string>();
                    var args_names = new List<string>();
                    if (cm.args_types != null)
                        for (int i = 0; i < cm.args_types.Length - 1; i++)
                        {
                            args.Add($"{APIWrapper.getTypeString(cm.args_types[i], custom_classes)} {cm.args_names[i]}" + (Convert.IsDBNull(cm.default_args[i]) ? "" : $" = {APIWrapper.getDefaultValueString(cm.default_args[i])}"));
                            args_names.Add(cm.args_names[i]);
                        }

                    string has_parameters_separator = (args.Count == 0 ? "" : ", ");

                    // generates a signature this the this extension method
                    string s_returntype = APIWrapper.getTypeString(cm.returntype, custom_classes);
                    bool b_hasresult = APIWrapper.getTypeString(cm.returntype, custom_classes) != "void";
                    string s_taskreturn = (b_hasresult ? $"Task<{s_returntype}>" : "Task");
                    string s_func_signature = $"public async {s_taskreturn} {cm.method}({string.Join(", ", args)})\n";

                    string s_args_names = string.Join(",", args_names);

                    // Body
                    string s_body = "";
                    if (b_hasresult)
                    {
                        s_body = $@"{{
    var call = RPCCall.Create({cm.method_id},true{has_parameters_separator}{s_args_names});
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = ""{cm.method}"";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return default({s_returntype});
    if (result == null)
        throw new RPCException(""Error in RPC Call '{cm.method}'"");

    {s_returntype} conv_result = result.ConvertedResult!=null ? ({s_returntype}) result.ConvertedResult : default;

    return conv_result;
}}";
						sb_functions.Append(s_func_signature);
						sb_functions.Append(s_body);
						sb_functions.Append("\n\n");

						// Optionally generate the special _NoResult stub (same call but will not process the result)
						bool generate_no_result = false;
                        if (cm.attribute is APIMethodAttribute)
                            generate_no_result = ((APIMethodAttribute)cm.attribute).GenerateNoResult;

                        if (generate_no_result)
                        {
                            s_func_signature = $"public async Task {cm.method}_NoResult({string.Join(", ", args)})\n";

                            s_body = $@"{{
    var call = RPCCall.Create({cm.method_id},false{has_parameters_separator}{s_args_names});
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = ""{cm.method}"";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return;
    if (result == null)
        throw new RPCException(""Error in RPC Call '{cm.method} NO RESULT'"");

}}";
                            sb_functions.Append(s_func_signature);
                            sb_functions.Append(s_body);
                            sb_functions.Append("\n\n");
                        }
					}
                    else
                    {

                        s_body = $@"{{
    var call = RPCCall.Create({cm.method_id},false{has_parameters_separator}{s_args_names});
    RPCResult result = await Call(call);
#if DEBUG
    call.Name = ""{cm.method}"";
#endif
    if (result!=null && result.Equals(RPCResult.InterBatchResult))
        return;
    if (result == null)
        throw new RPCException(""Error in RPC Call '{cm.method}'"");

}}";                    
                        sb_functions.Append(s_func_signature);
						sb_functions.Append(s_body);
						sb_functions.Append("\n\n");
					}

                    System.Diagnostics.Debug.WriteLine(s_func_signature);
                }
                catch (Exception ex)
                { }
            }

            if (opts.GenerateCallEnum)
                sb_functions_enum.Append("\n}");

            // Exports used classes
            var sb_customclasses = new StringBuilder();
            if (opts.IncludeClasses)
                for (int i = 0; i < custom_classes.Count; i++)
                {
                    Type t = custom_classes[i];

                    if (opts.excluded_classes != null && opts.excluded_classes.Contains(t))
                        continue;

                    if (opts.excluded_namespaces != null && opts.excluded_namespaces.Contains(t.Namespace))
                        continue;

                    if (t.IsArray)
                        t = t.GetElementType();
                    else if (t.IsGenericType)
                        t = t.GetGenericTypeDefinition();

                    string class_source = APIWrapper.getClassSource(t, custom_classes);

                    if (!string.IsNullOrEmpty(class_source))
                        sb_customclasses.Append(class_source + "\n\n");
                }

            // Assembles the final sourcefile
            var sb_file = new StringBuilder();

            sb_file.AppendLine(s_usings);
            sb_file.AppendLine(s_namespace_start);

            sb_file.AppendLine("\n#if !TABULARPC_EXCLUDESERIALIZEDCLASSES\n");

            // on mobile no serialization attribute is present
            sb_file.AppendLine("\n#if __MOBILE__\npublic partial class SerializableAttribute : Attribute { }\n#endif\n");

            sb_file.AppendLine(sb_customclasses.ToString());
            sb_file.AppendLine("\n#endif");

            sb_file.AppendLine(s_class_start);
            sb_file.AppendLine(sb_functions_enum.ToString());
            sb_file.AppendLine(sb_functions.ToString());

            // OSC writing was here
            
            sb_file.AppendLine(s_class_end);
            sb_file.AppendLine(s_namespace_end);


            File.WriteAllText(opts.OutputFile, sb_file.ToString(), Encoding.UTF8);
        }

		#endregion
	}
}