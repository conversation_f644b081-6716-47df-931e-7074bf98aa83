using System;
using System.Threading.Tasks;
using kcp2k;
using Tabula.PMCore;
using Tabula.SharedObjectMap;
using Tabula;

namespace Tabula.PMCore.Unity
{
    /// <summary>
    /// .NET equivalent of Unity's PMController_Client without MonoBehaviour dependency
    /// Provides the same networking functionality for console applications
    /// </summary>
    public class NetPMController_Client : IDisposable
    {
        public Model Model => Client?.Model;
        public ModelView View => Client?.View;

        // Clients 
        public SharedObjectMap_Client<Model, ModelView, Tabula.PMCore.PMCore_Client> Client;

        public Tabula.PMCore.PMCore_Client ClientRPC => Client?.Client;
        public KcpClient ClientKCP => Client?.Client?.Client?.NativeClient;

        public bool IsModelReady { get; private set; } = false;
        public bool IsConnected { get; private set; } = false;

        private bool _disposed = false;

        public NetPMController_Client()
        {
            Initialize();
        }

        private void Initialize()
        {
            // Create the client
            Client = new SharedObjectMap_Client<Model, ModelView, Tabula.PMCore.PMCore_Client>();
        }

        #region Connect

        /// <summary>
        /// Async connection method - .NET equivalent of Unity's ConnectCR()
        /// </summary>
        /// <param name="address">Server address</param>
        /// <param name="port">Server port (-1 for default)</param>
        /// <param name="result">Result object to track completion status</param>
        /// <param name="maxTimeSeconds">Maximum time to wait in seconds</param>
        /// <returns>Task that completes when connection attempt finishes</returns>
        public async Task ConnectAsync(string address, int port = -1, AsyncTaskResult result = null, float maxTimeSeconds = -1)
        {
            try
            {
                var connected = await NetAsyncUtilities.WaitForTaskAsync(
                    () => _connect_task(address, port).Result, 
                    result, 
                    maxTimeSeconds);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"NetPMController_Client: Connection failed - {ex.Message}");
                if (result != null)
                {
                    result.Exception = ex;
                    result.TaskIsFaulted = true;
                }
            }
        }

        /// <summary>
        /// Direct async connection method
        /// </summary>
        /// <param name="address">Server address</param>
        /// <param name="port">Server port (-1 for default)</param>
        /// <returns>True if connection successful</returns>
        public async Task<bool> ConnectDirectAsync(string address, int port = -1)
        {
            return await _connect_task(address, port);
        }

        // Connect to PMCore server, general connection it is not a player login
        private async Task<bool> _connect_task(string address, int port = -1)
        {
            IsConnected = false;

            // Note: No Unity Dispatcher needed in console app
            // Dispatcher.Set(UnityMainThreadDispatcher.Instance());

            var info = new ConnectionInfo()
            {
                server_address = address.Trim(),
                rpc_port = (port == -1 ? ClientServer_Constants.RPCServerPort : port),
                receive_updates = false // we are not interested since we don't receive the model
            };

            try
            {
                // Client connection, it is sufficient for the API
                var connection_result = await Client.Connect(info);

                // Negative ids are errors!
                if (connection_result > 0)
                {
                    try
                    {
                        // "ConnectOnly" connection instead of ReceiveModel() will JUST setup the update server
                        Client.ConnectOnly();
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"NetPMController_Client: Cannot setup update server - {ex.Message}");
                        return false;
                    }

                    Console.WriteLine("NetPMController_Client: Connected");
                    IsConnected = true;
                    return true;
                }
                else
                {
                    Console.WriteLine("NetPMController_Client: Cannot connect");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"NetPMController_Client: Connection error - {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Disconnect

        /// <summary>
        /// Disconnect from the server
        /// </summary>
        public void Disconnect()
        {
            try
            {
                if (Client != null)
                {
                    Client.Disconnect();
                    IsConnected = false;
                    IsModelReady = false;
                    Console.WriteLine("NetPMController_Client: Disconnected");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"NetPMController_Client: Disconnect error - {ex.Message}");
            }
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    Disconnect();
                    Client?.Dispose();
                }
                _disposed = true;
            }
        }

        ~NetPMController_Client()
        {
            Dispose(false);
        }

        #endregion
    }
}
