﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Tabula;
using Tabula.SharedObjectMap;
using System.Linq;
using Tabula.PMCore.Unity;

// The server method are defined with partial classes on the server


namespace Tabula.PMCore
{
    public static class Defaults
    {
        public static int RPCServerPort = 16100;
        public static int OSCServerPort = 16200;
        public static int HTTPServerPort = 8080;
    }

	// FIXME: deprecate and generalize?    
	/*
    public enum ApplicationStatus
    {
        None = 0,
        Edit = 1,   // Editing and calibration
        Play = 2    // Game play
    } */

	// This is the basic "Project"
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public partial class Model : Tabula.SharedObjectMap.GuidObject
    {
        public int Version;             // SARGAME protocol version (editor and module must use the same, incremented when there are breaking additions or modifications)
        public int TemplateVersion;     // Project template version (to trigger a new copy of project files from template folder, useful to force updated projects)

        public string ProjectName;      // for scenarios, this is the verbose name

        public string ScenarioId;       // scenario identifier (calibration and structures) Screen, OutputSurfaces, Structures

        // The whole virtual screen size, mapped to one or more ouputsurfaces
        public Screen Screen;

        // Surfaces configuration (the "Screens")
        // NOTE: just one at the moment
        public List<OutputSurface> OutputSurfaces;

        // For runtime
		#region Editing

		// transmitting the visible cursor when interacting
		public Cursor Cursor;

        // for structure/contour drawing, one at a time
        public Polygon Polygon;

        // the status of the focus for the module (player)
        [JsonIgnore]
        public bool ModuleFocus = false;

		#endregion

        // Templates to clone for entities, with default parameters, searched by type
        public List<Entity>     Templates;

		#region Structures

		// Physical structures
		public List<Structure>  Structures;

		#endregion

		#region Entities

        public List<Entity> Entities;

		#endregion

		#region Normalization

		// populates the model
		public void SetDefaults()
        {
            ProjectName = "<undefined>";

            var standard_screen_size = new SizeI() { width = 1920, height = 1080 };

            Screen = new Screen()
            {
                size = standard_screen_size,

                // TODO: re-enter some pixels
                screen_markers = new List<ScreenMarker>()
                { new ScreenMarker() { position = new Vector2f(0, 0) },
                    new ScreenMarker() { position = new Vector2f(standard_screen_size.width, 0) },
                    new ScreenMarker() { position = new Vector2f(standard_screen_size.width, standard_screen_size.height) },
                    new ScreenMarker() { position = new Vector2f(0, standard_screen_size.height) }
                },

                image_markers = new List<ImageMarker>()
			};

            for (int i = 0; i < Screen.MAX_IMAGE_MARKERS; i++)
                Screen.image_markers.Add(new ImageMarker() { position = new Vector2f(0,0), enabled = false });

            OutputSurfaces = new List<OutputSurface>() { new OutputSurface() };
            OutputSurfaces[0].aspect = 1;
            OutputSurfaces[0].vertices = new List<Vector2i> { new Vector2i(0, 0), new Vector2i(Screen.size.width, 0), new Vector2i(Screen.size.width, Screen.size.height), new Vector2i(0, Screen.size.height) };

            Structures = new List<Structure>();

            Cursor = new Cursor();

            Polygon = new Polygon();           

            Templates = new List<Entity>();

            Entities = new List<Entity>();
        }

        // Makes sure the loaded scenario has the needed objects created
        public void Normalize()
        {
			// TODO: set to current screen size?
			var standard_screen_size = new SizeI() { width = 1920, height = 1080 };

			// Screen
			if (Screen == null)
            {               
				Screen = new Screen()
				{
					size = standard_screen_size,

					screen_markers = new List<ScreenMarker>()
					{ new ScreenMarker() { position = new Vector2f(0,0),},
					  new ScreenMarker() { position = new Vector2f(standard_screen_size.width,0) },
					  new ScreenMarker() { position = new Vector2f(standard_screen_size.width,standard_screen_size.height) },
					  new ScreenMarker() { position = new Vector2f(0,standard_screen_size.height) }
					},

					image_markers = new List<ImageMarker>()
				};
			}

            if (Screen.screen_markers == null || (Screen.screen_markers!=null && Screen.screen_markers.Count==0))
                Screen.screen_markers = new List<ScreenMarker>()
                    { new ScreenMarker() { position = new Vector2f(0,0),},
                      new ScreenMarker() { position = new Vector2f(standard_screen_size.width,0) },
                      new ScreenMarker() { position = new Vector2f(standard_screen_size.width,standard_screen_size.height) },
                      new ScreenMarker() { position = new Vector2f(0,standard_screen_size.height) }
                    };

			// check calibration points are valid
			foreach (var cp in Screen.screen_markers)
            {
                if (cp.position == null)
                    cp.position = new Vector2f(0, 0);
            }

            if (Screen.image_markers == null || (Screen.image_markers != null && Screen.image_markers.Count==0))
                Screen.image_markers = new List<ImageMarker>();

            // normalize up to 10
			for (int i = Screen.image_markers.Count; i < Screen.MAX_IMAGE_MARKERS; i++)
				Screen.image_markers.Add(new ImageMarker() { position = new Vector2f(0, 0), enabled = false });

			foreach (var cp in Screen.image_markers)
			{
                if (cp.position == null)
                {
                    cp.position = new Vector2f(0, 0);
                    cp.enabled = false;
                }
			}

			// Output surfaces
			if (OutputSurfaces == null || OutputSurfaces.Count == 0)
            {
                OutputSurfaces = new List<OutputSurface>() { new OutputSurface() };
                OutputSurfaces[0].aspect = 1;
                OutputSurfaces[0].vertices = new List<Vector2i> { new Vector2i(0, 0), new Vector2i(Screen.size.width, 0), new Vector2i(Screen.size.width, Screen.size.height), new Vector2i(0, Screen.size.height) };
            }

            if (Structures == null)
			    Structures = new List<Structure>();

            if (Cursor == null)
			    Cursor = new Cursor();

            if (Polygon == null)
			    Polygon = new Polygon();

            if (Templates == null)
			    Templates = new List<Entity>();

            if (Entities == null)
			    Entities = new List<Entity>();
		}

		#endregion

		#region Entities search and add

		// Searches entity tree for name && !! type
		public List<Entity> SearchEntities(string name=null, string type=null, Entity.Flags flags = Entity.Flags.None)
        {
            void _search_entity(Entity entity, List<Entity> _found_entities)
            {
                if (entity.items != null)
                    foreach (var e in entity.items)
                    {
                        bool name_ok = (string.IsNullOrEmpty(name) || (!string.IsNullOrEmpty(name) && e.name == name));
                        bool type_ok = (string.IsNullOrEmpty(type) || (!string.IsNullOrEmpty(type) && e.type == type));
                        bool flags_ok = (flags == Entity.Flags.None || (flags != Entity.Flags.None && e.flags.HasFlag(flags)));

                        if (name_ok && type_ok && flags_ok)
                        {
                            _found_entities.Add(e);
                        }

                        _search_entity(e, _found_entities);
                    }
            }


            List<Entity> found_entities = new List<Entity>();

            foreach (var e in Entities)
                _search_entity(e, found_entities);

            return found_entities;
        }

        public void AddEntityIfNotExist(Entity entity, string entity_context="Entities", string name = "", string type = "", Entity.Flags flags = Entity.Flags.None)
        {
            var found_entities = SearchEntities(name, type);

            if (found_entities.Count == 0)
                AddEntity(entity, entity_context);
        }

        public void AddEntity(Entity entity, string entity_context)
        {
            // Makes sure entity context root exists
            // NOTE: working on Model not wrappers because it is before SetModel()
            var root = (from e in Entities where e.name == entity_context select e).FirstOrDefault();
            if (root == null)
            {
                root = new Entity()
                {
                    name = entity_context,
                    flags = Entity.Flags.None,
                    items = new List<Entity>()
                };

                Entities.Add(root);
            }

            root.items.Add(entity);
        }

		#endregion

		#region Helpers

        // Calculates real screen size depending on offset 
        public RectI GetVisualScreenSize(int output_surface_idx=0)
        {
            if (output_surface_idx >= OutputSurfaces.Count)
                return default;

            var os = OutputSurfaces[output_surface_idx];

            return new RectI()
            {
                x = (int) ((float) os.x_offset * (float) Screen.size.width),
				y = (int) ((float) os.y_offset * (float) Screen.size.height),				
			    width = (int)((float)Screen.size.width * os.x_tiling),
				height = (int)((float)Screen.size.height * os.y_tiling)
		    };
		}

		public const int ScreenMarkersSafeAreaBorders = 60;

		// Returns the rectangle where marker should be in to be clearly taken from the picture and detected
		public RectI GetVisualMarkerSafeArea(int output_surface_idx=0)
        {
            var screen_rect = GetVisualScreenSize(output_surface_idx);

            screen_rect.x += ScreenMarkersSafeAreaBorders;
			screen_rect.y += ScreenMarkersSafeAreaBorders;
			screen_rect.width -= ScreenMarkersSafeAreaBorders*2;
			screen_rect.height -= ScreenMarkersSafeAreaBorders*2;

            return screen_rect;
		}

        public Entity FindEntityByName(string name)
        {
            if (Entities == null)
                return null;

            foreach (var e in Entities)
            {
                var found = e.FindEntityByName(name);
                if (found != null)
                    return found;
            }

            return null;
        }


		#endregion

	}

    #region Player and Input

    [Tabula.API.APIWrapper.SkipInContractGeneration]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public partial class PlayerJoinResult
    {
        public const int ReasonAccept_NewPlayer = 1;
		public const int ReasonAccept_ExistingPlayer = 2;

		public const int ReasonDeny_General = -1;           // general deny
		public const int ReasonDeny_JoinDisabled = -2;      // not the right moment
		public const int ReasonDeny_NetworkingDisabled = -3;      // networking disabled (for players)
		public const int ReasonDeny_TooMany = -4;           // too many waiting players

		public int   reason;
        public int   player_client_id;  // the unique id to be used for direct communication (raw packets to networked players)
        public int   player_flags;      // flags containig info about enabled input modes and buttons

        public PlayerJoinResult() { }

		public PlayerJoinResult(int _reason, int _player_client_id=0, 
            int _player_flags = PlayerControllerMessage.Flags_Default)
        { 
            reason = _reason; 
            player_client_id = _player_client_id; 
            player_flags = _player_flags;
        }
    }

	[Tabula.API.APIWrapper.SkipInContractGeneration]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public partial class PlayerLeftResult
    {
        public const int ReasonLeft_ClientLeft = 1;             // the client requested a leave
		public const int ReasonLeft_ClientDisconnected = 2;     // ?
		public const int ReasonLeft_KeepAliveExpired = 3;

		public const int ReasonLeft_DenyClientNotFound = -1;    // NOTE: this will happen if there is a leave request from the client after a death notification

		public int reason;

		public PlayerLeftResult() { }

		public PlayerLeftResult(int _reason) { reason = _reason; }
	}


	#endregion

	#region Generic

	// For getting statistics and other data
	[Tabula.API.APIWrapper.SkipInContractGeneration]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class NamedValue
	{
		public string name;
		public object value;

        public bool AsBool => Convert.ToBoolean(value);
        public int AsInt => Convert.ToInt32(value);
		public float AsFloat => Convert.ToSingle(value);
		public double AsDouble => Convert.ToDouble(value);
		public string AsString => Convert.ToString(value);

		public static bool GetBool(List<NamedValue> list, string name)
			=> (bool) (from s in list where s.name == name select s).FirstOrDefault()?.AsBool;

		public static int GetInt(List<NamedValue> list, string name)
            => (int) (from s in list where s.name==name select s).FirstOrDefault()?.AsInt;

		public static float GetFloat(List<NamedValue> list, string name)
			=> (float)(from s in list where s.name == name select s).FirstOrDefault()?.AsFloat;

		public static double GetDouble(List<NamedValue> list, string name)
			=> (double)(from s in list where s.name == name select s).FirstOrDefault()?.AsDouble;

		public static string GetString(List<NamedValue> list, string name)
			=> (string)(from s in list where s.name == name select s).FirstOrDefault()?.AsString;

	}

	#endregion

	#region General Classes

	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class RectF : Tabula.SharedObjectMap.GuidObject
	{
        public float x, y;
		public float width, height;
	}

	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class RectI : Tabula.SharedObjectMap.GuidObject
	{
		public int x, y;
		public int width, height;
	}

	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class SizeF : Tabula.SharedObjectMap.GuidObject
    {
        public float width, height;
    }

	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class SizeI : Tabula.SharedObjectMap.GuidObject
    {
        public int width, height;
    }

	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class Vector2i : Tabula.SharedObjectMap.GuidObject
    {
        public int x, y;

        public Vector2i() { }

        public Vector2i(int _x, int _y)
        {
            x = _x; y = _y;
        }

        public Vector2i(float _x, float _y)
        {
            x = (int) _x; y = (int) _y;
        }
    }

	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class Vector2f : Tabula.SharedObjectMap.GuidObject
    {
        public float x, y;

        public Vector2f() { }
        public Vector2f(float _x, float _y)
        {
            x = _x; y = _y;
        }
    }

    #endregion

    #region Mapping and Surfaces

    [GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class Screen : Tabula.SharedObjectMap.GuidObject
    {
        public SizeI size;

        // markers for homography
        public List<ScreenMarker>   screen_markers;     // always 4
		public List<ImageMarker>    image_markers;      // for ease of UI, a maximum number of image_markers is created and only a certain number used

        public const int MAX_IMAGE_MARKERS = 10;

		public Media? calibration_image;    // the reference image of the wall

        public float calibration_image_opacity = 1f;    // opacity of the calibration image (wall) in the editor

        public float background_mask_opacity = 0f;      // opacity of the background obsucrer
    }

	[GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class ScreenMarker : Tabula.SharedObjectMap.GuidObject
	{
		public Vector2f? position;   // position of calibration point on screen
	}

	[GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class ImageMarker : Tabula.SharedObjectMap.GuidObject
	{
		public Vector2f? position;   // position of calibration point on reference image
        public bool enabled;         // true if it is used in the calibration
	}

	[GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class Media : Tabula.SharedObjectMap.GuidObject
	{        
        public string   file;
        public SizeI?   image_size;
        public DateTime time;
	}

    [GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public partial class OutputSurface : Tabula.SharedObjectMap.GuidObject
    {
        // 4 corners for warping whole screen
        public List<Vector2i> vertices = new List<Vector2i>();

        // aspect
        public float aspect;

        // tiling and offset
        public float x_tiling=1;
        public float x_offset=0;
        public float y_tiling=1;
        public float y_offset=0;
    }

    // Helper/temporary class for creating structures / contours
    [GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public partial class Polygon : Tabula.SharedObjectMap.GuidObject
	{
        public bool enabled;

        // list of dynamically added points
        public List<Vector2i> vertices = new List<Vector2i>();
    }

    #endregion

    #region Structures

    [GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public partial class Structure : Entity
    {
		// at least 3 vertices  in clockwise order
		public List<Vector2f> vertices = new List<Vector2f>();

        // NOTE: effects are defined in base class as data, the implementation rules
        public bool     is_physical = true;         // by default every structure is physical (has a collider)
        public bool     is_mask = false;            // shortcut for using the structure as a black mask (black color, no effects or contour)

        // parameters, divided by base and future new shaders
        public StructureParams0   params0 = new StructureParams0();
        public StructureParams1   params1 = new StructureParams1();

		[JsonIgnore]
        public bool selected_for_editing = false;

		[JsonIgnore]
		public bool IsOverriding =>
			params0.texture_enable || params0.texture_mask_enable || params0.effect_enable || params0.effect2_enable || params0.contour_enable || params0.color_animation_enable || params0.color_enable || params0.material_enable;

		// position is still used to move the whole structure, but it is not visually significant        
	}

    // Classes for serialized structure params, depends on shader used

    // Base parameters, effects
    [Serializable]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class StructureParams0 : Tabula.SharedObjectMap.GuidObject
	{
		public bool     texture_enable = false;
		public string   texture;

		public bool     texture_mask_enable = false;
		public string   texture_mask;

        public bool     effect_enable = false;
		public int      effect = -1;                // type of mesheffect (-1 none) for contour (or contour + front face)
		public float    effect_scale = 1.0f;              // effect scale

		public bool     effect2_enable = false;
		public int      effect2 = -1;               // type of mesheffect (-1 none) for front face
		public float    effect2_scale = 1.0f;              // effect2 scale

		public bool     contour_enable = false;
		public int      contour = -1;               // type of contour (-1 none)

		public bool     color_animation_enable = false;
		public int      color_animation = -1;       // type of color animation (-1 none)
		public float    color_animation_speed = 1;  // speed of the color animation        

		public bool     color_enable = false;
		public string   color = "#FFFFFFFF";                 // hexadecimal

		public bool     material_enable = false;
		public int      material = -1;        
	}

	// First standard shader PMStructureEffectShader
	[Serializable]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class StructureParams1 : Tabula.SharedObjectMap.GuidObject
	{
		public float    rotation = 0;
		public bool     texture_mask_invert = false;
		public bool     texture_screenspace = false;
		public float    opacity = 0;

        // TODO:...
	}

	#endregion

	#region Entities

	// Base class for positionable objects
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public abstract class Visual : Tabula.SharedObjectMap.GuidObject
    {
        public Vector2f? position;
        public SizeF?    size;              // defaults to single point
        public Vector2f? offset;            // offset of position (icons, bounding boxes..)

        public virtual void Normalize()
        {
            if (position == null)
                position = new Vector2f(0, 0);

            if (size == null)
                size = new SizeF();

            if (offset == null)
                offset = new Vector2f(0, 0);
        }
    }


    [GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class Cursor : Visual
    {
        public bool draw = true;            // TODO: move in Visual ?
    }

	[GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class EntityCreate : Tabula.SharedObjectMap.GuidObject
	{
		public string template;     // the template to create
	}

	[GuidObjectClass]
	[Tabula.API.APIWrapper.SkipInContractGeneration]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public partial class Entity : Visual
    {
        [Flags]
        [JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        public enum Flags
        {
            None = 0,
            HasVisual,              // entity has a visual representation
            ShowWhenSelected,       // visual representation is only visualized when selected
            ShowInEditor,           // for future replacement of show_editor
            ShowAsZone              // shows the zone and not the scaled icon
        }

        public string name;         // IMPORTANT: if name is prepended with # (ex: #myobject) then it is a REFERENCE of an existing object in scene, not instantiated with prefab, a component will be added
        public string type;
        public string container;    // gameobject container for creation
                
        public string icon;

		public bool show_editor = true; // shows in hierarchy

		public string name_desc;    // the name visualized

        public bool   active = true;        // gameobject active
        public bool   sync_fields = true;   // field synch behaviour (true by default)

        [GuidObjectCollection]  // per permettere il polimorfismo nella collezione
        public List<object>? fields;    // lista di parametri di questa entità

        public List<string>?  fields_shown;  // fields that will be shown (if null it is ignored, if empty NO field will be shown)

        public List<Entity>? items;     // entità figlie

        public Flags flags = Flags.None;    // flags

        public bool is_template = false;   

        // Group 
        // if defined in an entity template will add it to the create
        public string? group;

        // Create
        // if defined this entity can create the listed entities (will be filled on the editor's side looking at the template group)
        public List<EntityCreate>? create;

        // Presets
        public List<Preset>? presets;
        public string? preset;  // selected preset

        [JsonIgnore]
        public bool IsReference => name.StartsWith("#");


        private Dictionary<string, dynamic> _fields = new Dictionary<string, dynamic>();

        public override void Normalize()
        {
            base.Normalize();
        }

        public void AddField(string name, Field field)
        {
            if (fields == null)
                fields = new List<object>();

            field.name = name;
            fields.Add(field);
        }

		public T GetField<T>(string field_name) where T: Field
		{
            // caching
            if (_fields.TryGetValue(field_name, out dynamic cached_field))
            {
                return (T) cached_field;
            }
            else
            {
                dynamic field = (from f in fields.OfType<Field>() where f.name == field_name select f).FirstOrDefault();
                if (field != null)
                    _fields.Add(field_name, field);

                return (T) field;
            }
		}

		public T GetFieldValue<T>(string field_name)
        {
            // caching
            if (_fields.TryGetValue(field_name, out dynamic cached_field))
            {
                return (T)cached_field.value;
            }
            else
            {
                dynamic field = (from f in fields.OfType<Field>() where f.name == field_name select f).FirstOrDefault();
                if (field != null)
                    _fields.Add(field_name, field);
                else
                    return default;

                return (T)field.value;
            }
        }

        public object GetFieldValue(string field_name)
		{
            // caching
            if (_fields.TryGetValue(field_name, out dynamic cached_field))
            {
                return cached_field.value;
            }
            else
            {
                dynamic field = (from f in fields.OfType<Field>() where f.name == field_name select f).FirstOrDefault();
                if (field != null)
                {
                    _fields.Add(field_name, field);
                    return field.value;
                }
                else
                    return null;
            }
        }

		public Entity FindEntityByName(string name_to_search)
		{
			if (name == name_to_search)
				return this;

            if (items != null)
                foreach (var i in items)
                {
                    var found = i.FindEntityByName(name_to_search);
                    if (found != null)
                        return found;
                }

            return null;
		}
	}

	#endregion

	#region Fields

	// Attribute to flag fields so that they are automatically mapped
	public class MapFromEntityFieldAttribute : Attribute
	{
		public Type FieldType;
	}

	// just a base class, doesn't need view/wrappers
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public partial class Field : Tabula.SharedObjectMap.GuidObject
    {
        public string   name;               // real mapped name
        public string?  name_desc;          // descriptive name (for editor)
        public string?  info;               // shown information (ex: right side)
        public string?  tooltip;            // tooltip information
        public string?  tag;                // generic tag (es: advanced settings..)

        public string?  group;              // grouping in editor
        public string?  license_feature;    // shown only if the license has this feature

		public bool show_editor = true; // another way to specify it to be shown in editor


		public bool IsExpanded = false;
        public bool IsSelected = false;

        public virtual void SetValue(object v, MapFromEntityFieldAttribute attr=null)
		{}

        public virtual object GetValue()
        { return null; }

        public static Type GetFieldForType(Type type)
		{
            if (type == typeof(string))
                return typeof(StringField);
            else if (type == typeof(int))
                return typeof(IntegerField);
            else if (type == typeof(float))
                return typeof(FloatField);
            else if (type == typeof(bool))
                return typeof(BoolField);
            
            return null;
        }
    }

    [GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public partial class ButtonField : Field
    {
        public string action;   // action called
    }

    [GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public partial class StringField : Field
    {
        public string value;


		public override void SetValue(object v, MapFromEntityFieldAttribute attr = null)
		{
            value = (string)Convert.ChangeType(v, typeof(string));
		}

		public override object GetValue()
		{
            return value;
		}
	}

    [GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public partial class IntegerField : Field
    {
        public int value;

        // clamping, applied on the client side
        public int min, max;

        public override void SetValue(object v, MapFromEntityFieldAttribute attr = null)
        {
            value = (int) Convert.ChangeType(v, typeof(int));
        }

        public override object GetValue()
        {
            return value;
        }
    }

    [GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public partial class FloatField : Field
    {
        public float value;

        // clamping, applied on the client side
        public float min, max;

		public override void SetValue(object v, MapFromEntityFieldAttribute attr = null)
        {
            value = (float) Convert.ChangeType(v, typeof(float));
		}

        public override object GetValue()
        {
            return value;
        }
    }

    [GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public partial class BoolField : Field
    {
        public bool value;

        public override void SetValue(object v, MapFromEntityFieldAttribute attr = null)
        {
            value = (bool) Convert.ChangeType(v, typeof(bool));
		}

        public override object GetValue()
        {
            return value;
        }
    }

	public class SliderFloatFieldAttribute : MapFromEntityFieldAttribute
	{
		public float min = 0.2f, max = 3.0f;

		public SliderFloatFieldAttribute()
		{
			FieldType = typeof(SliderFloatField);
		}
	}

	[GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public partial class SliderFloatField : Field
    {
        public float value;
        public float min, max;

        public override void SetValue(object v, MapFromEntityFieldAttribute attr = null)
        {
            value = (float)Convert.ChangeType(v, typeof(float));

            var slider_attr = attr as SliderFloatFieldAttribute;
            min = slider_attr.min;
            max = slider_attr.max;
        }

        public override object GetValue()
        {
            return value;
        }
    }

	[GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public partial class SliderFloatPercentageField : SliderFloatField
	{
		public float percentage_factor;
	}

	[GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public partial class ChoiceField : StringField
	{
        public string[] choice_keys;
		public string[] choice_values;
	}

	[GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public partial class Position2DField : Field
    {
        public Vector2f position;

        public override void SetValue(object value, MapFromEntityFieldAttribute attr = null)
        {
            value = (Vector2f) value;
        }

        public override object GetValue()
        {
            return position;
        }
    }

	public class FileFieldAttribute : MapFromEntityFieldAttribute
	{
		public FileFieldAttribute()
		{
			FieldType = typeof(FileField);
		}
	}

	[GuidObjectClass]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public partial class FileField : Field
	{
		public string value;    // path

		public override void SetValue(object v, MapFromEntityFieldAttribute attr = null)
		{
			value = (string) Convert.ChangeType(v, typeof(string));
		}

		public override object GetValue()
		{
			return value;
		}
	}

	#endregion

	#region Presets

	// Entities can optionally have presets, collections of values for their fields
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class Preset : Tabula.SharedObjectMap.GuidObject
    {
        public string   name;
        public bool     is_readonly;    // for default presets
		public Dictionary<string, object>? fields;
	}

	#endregion

	#region Calibration

	[Serializable]
	[System.Reflection.ObfuscationAttribute(Exclude = true)]
	public class CalibrationSnapshot
	{
		public SizeI screen_full;
		public RectI screen_crop;

		public List<Vector2i> markers;
	}

	#endregion
}
