﻿//TABULA_GUID:{F108DEB6-BBB8-4CC7-8F00-AFCD8BCA117B}
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Text;
#if UNITY_EDITOR
    using UnityEditor;
using UnityEditorInternal;
#endif
using UnityEngine;

// Version 1.1 (21/5/2018) Added GUI debug draw functions

namespace Tabula
{
	public class CoroutineWaitForTaskResult
	{
		public bool TaskIsFaulted = false;
		public bool TaskIsCanceled = false;

		public Exception Exception;

        public bool HasTimedOut => Exception != null && Exception is TimeoutException;
	}

	public static class UnityUtilities
    {
		#region Math

		public static float RemapClamped(this float aValue, float aIn1, float aIn2, float aOut1, float aOut2)
		{
			float t = (aValue - aIn1) / (aIn2 - aIn1);
			t = Mathf.Clamp(t, 0f, 1f);
			return aOut1 + (aOut2 - aOut1) * t;
		}

		public static float Remap(float value, float daMin, float daMax, float aMin, float aMax)
		{
			// Assicurati che il valore sia all'interno dell'intervallo sorgente
			value = Mathf.Max(daMin, Mathf.Min(daMax, value));

			// Calcola il valore rimappato
			float percentuale = (value - daMin) / (daMax - daMin);
			return aMin + percentuale * (aMax - aMin);
		}

		#endregion


		#region Vector3

		public static Vector3 RandomizeRangeZAxis(this Vector3 v, float angle_min, float angle_max)
        {
			float randomAngle = UnityEngine.Random.Range(angle_min, angle_max);

			// Create a Quaternion rotation around the Z-axis
			Quaternion randomRotation = Quaternion.Euler(0f, 0f, randomAngle);

			// Apply the rotation to a normalized vector
			Vector3 randomizedVector = randomRotation * v;

			// Normalize the result to ensure it's still a unit vector
			randomizedVector.Normalize();

            return randomizedVector;
		}

		#endregion



		// Extension to Transform to set only some vector values

		// Transform's vector getters in Unity return vectors by value, so we can
		// only change them by assigning a complete vector and not individual
		// components.  WAR this with a bunch of helper extensions.
		public static void LocalPositionSetX(this Transform t, float s) { Vector3 v = t.localPosition; v.x = s; t.localPosition = v; }
        public static void LocalPositionSetY(this Transform t, float s) { Vector3 v = t.localPosition; v.y = s; t.localPosition = v; }
        public static void LocalPositionSetZ(this Transform t, float s) { Vector3 v = t.localPosition; v.z = s; t.localPosition = v; }

        public static void LocalScaleSetX(this Transform t, float s) { Vector3 v = t.localScale; v.x = s; t.localScale = v; }
        public static void LocalScaleSetY(this Transform t, float s) { Vector3 v = t.localScale; v.y = s; t.localScale = v; }
        public static void LocalScaleSetZ(this Transform t, float s) { Vector3 v = t.localScale; v.z = s; t.localScale = v; }

        public static void LocalScaleMulX(this Transform t, float s) { Vector3 v = t.localScale; v.x *= s; t.localScale = v; }
        public static void LocalScaleMulY(this Transform t, float s) { Vector3 v = t.localScale; v.y *= s; t.localScale = v; }
        public static void LocalScaleMulZ(this Transform t, float s) { Vector3 v = t.localScale; v.z *= s; t.localScale = v; }


        //NOTE: Since RectTransform is usually in screen-coords, if using in in worldspace (ex: ws canva) needs to explicitly ask for conversion
        static public Rect GetScreenRect(this RectTransform rt, bool world_to_screen = false, Camera camera = null) 
        {            
            Vector3[] corners = new Vector3[4];
            rt.GetWorldCorners(corners);

            if (world_to_screen)
            {
                if (camera == null)
                    camera = Camera.main;

                for (int c = 0; c < 4; c++)
                    corners[c] = camera.WorldToScreenPoint(corners[c]);
            }
            
            return new Rect(
            corners[0].x,
            corners[0].y,
            corners[2].x - corners[0].x,
            corners[2].y - corners[0].y);       
        }

        // Bounds extension methods (useful so that the right bound can be taken from the right renderer)

        // works in Ortho only
        // FIXME: not clear, depends if bounds is in world space or in local space! Renderer.bounds is world, Mesh.bounds is local
        /*
        public static Rect GetScreenRectOrtho(this Bounds bounds, Transform transform, Camera camera = null)
        {
            if (camera == null)
                camera = Camera.main;

            Vector3 _Center = transform.InverseTransformPoint(bounds.center);
            //if (_Center.z > 1)
            {
                Vector3 _UpperLeft = camera.WorldToScreenPoint(transform.TransformPoint(_Center + new Vector3(-bounds.extents.x, bounds.extents.y, 0)));
                Vector3 _LowerRight = camera.WorldToScreenPoint(transform.TransformPoint(_Center + new Vector3(bounds.extents.x, -bounds.extents.y, 0)));

                return new Rect(_UpperLeft.x, _UpperLeft.y - (_UpperLeft.y - _LowerRight.y), _LowerRight.x - _UpperLeft.x, _UpperLeft.y - _LowerRight.y);
            }
        }

        // FIXME: not clear, depends if bounds is in world space or in local space! Renderer.bounds is world, Mesh.bounds is local
        public static void GetWorldCornersOrtho(this Bounds bounds, Vector3[] wcorners, Transform transform, Camera camera = null)
        {
            if (camera == null)
                camera = Camera.main;

            Vector3 _Center = transform.InverseTransformPoint(bounds.center);

            Vector3 _UpperLeft = transform.TransformPoint(_Center + new Vector3(-bounds.extents.x, bounds.extents.y, 0));
            Vector3 _LowerRight = transform.TransformPoint(_Center + new Vector3(bounds.extents.x, -bounds.extents.y, 0));
            
            wcorners[0] = new Vector3(_UpperLeft.x, _LowerRight.y, 0);   // bottom-left,
            wcorners[1] = _UpperLeft;  // upper-left
            wcorners[2] = new Vector3(_LowerRight.x, _UpperLeft.y, 0);   // upper-right
            wcorners[3] = _LowerRight;  // lowe-right
        }

        public static Rect GetWorldRectOrtho(this Bounds bounds, Transform transform, Camera camera = null)
        {
            Vector3[] wcorners = new Vector3[4];
            bounds.GetWorldCornersOrtho(wcorners, transform, camera);

            Bounds wbounds = new Bounds();

            for (int i = 0; i < 4; i++)
                wbounds.Encapsulate(wcorners[i]);

            Rect rect = new Rect
            {
                yMax = wbounds.max.y
            };

            return rect;
        }*/

        // Object searching

        // Will find all objects in scene having a certain component, also roots and inactive ones     
        // Can be called with types of components or even interfaces

#if UNITY_EDITOR

        public static List<T> FindAllObjectsInScene<T>(bool includeInactive = true)
        {
            List<T> interfaces = new List<T>();
            GameObject[] rootGameObjects = UnityEngine.SceneManagement.SceneManager.GetActiveScene().GetRootGameObjects();
            foreach (var rootGameObject in rootGameObjects)
            {
                T[] childrenInterfaces = rootGameObject.GetComponentsInChildren<T>(includeInactive);
                foreach (var childInterface in childrenInterfaces)
                {
                    interfaces.Add(childInterface);
                }
            }
            return interfaces;
        }
#endif


        // Simple 2D drawing in OnGUI() functions

        private static readonly Texture2D backgroundTexture = Texture2D.whiteTexture;
        private static readonly GUIStyle textureStyle = new GUIStyle { normal = new GUIStyleState { background = backgroundTexture } };

        public static void DrawRect(Rect position, Color color, GUIContent content = null)
        {
            var backgroundColor = GUI.backgroundColor;
            GUI.backgroundColor = color;
            GUI.Box(position, content ?? GUIContent.none, textureStyle);
            GUI.backgroundColor = backgroundColor;
        }

        public static void LayoutBox(Color color, GUIContent content = null)
        {
            var backgroundColor = GUI.backgroundColor;
            GUI.backgroundColor = color;
            GUILayout.Box(content ?? GUIContent.none, textureStyle);
            GUI.backgroundColor = backgroundColor;
        }

        public static string ColorToHex(Color32 color)
        {
            string hex = color.r.ToString("X2") + color.g.ToString("X2") + color.b.ToString("X2");
            return hex;
        }

        public static Color HexToColor(string hex)
        {
            hex = hex.Replace("0x", "");//in case the string is formatted 0xFFFFFF
            hex = hex.Replace("#", "");//in case the string is formatted #FFFFFF
            byte a = 255;//assume fully visible unless specified in hex
            byte r = byte.Parse(hex.Substring(0, 2), System.Globalization.NumberStyles.HexNumber);
            byte g = byte.Parse(hex.Substring(2, 2), System.Globalization.NumberStyles.HexNumber);
            byte b = byte.Parse(hex.Substring(4, 2), System.Globalization.NumberStyles.HexNumber);
            //Only use alpha if the string has enough characters
            if (hex.Length == 8)
            {
                a = byte.Parse(hex.Substring(6, 2), System.Globalization.NumberStyles.HexNumber);
            }
            return new Color32(r, g, b, a);
        }

		public static int ColorToInt(Color color)
		{
			// Convert the color components to bytes
			byte r = (byte)(color.r * 255);
			byte g = (byte)(color.g * 255);
			byte b = (byte)(color.b * 255);
			byte a = (byte)(color.a * 255);

			// Combine the components into an integer
			int colorInt = (a << 24) | (r << 16) | (g << 8) | b;
			return colorInt;
		}

		public static Color IntToColor(int colorInt)
		{
			// Extract the components from the integer
			float a = ((colorInt >> 24) & 0xFF) / 255.0f;
			float r = ((colorInt >> 16) & 0xFF) / 255.0f;
			float g = ((colorInt >> 8) & 0xFF) / 255.0f;
			float b = (colorInt & 0xFF) / 255.0f;

			// Create the Color object
			return new Color(r, g, b, a);
		}

		// VECTOR EXTENSIONS

		// Versione funzionante e molto piu' corretta di quella di Façade che usa conversioni a long!
		public static bool IsScreenPointInScreenPolygon(this Vector3 p, Vector3[] screen_polygon, Camera camera=null)
        {
            if (camera == null)
                camera = Camera.main;

            p.y = Screen.currentResolution.height - p.y;

            Vector3[] poly = new Vector3[4];

            for (int i = 0; i < screen_polygon.Length; i++)
            {
                //poly[i] = camera.WorldToScreenPoint(WarpHandles[i].transform.position);   // TODO: world version?
                poly[i] = screen_polygon[i];
                poly[i].y = Screen.currentResolution.height - poly[i].y;    // FIXME: will work in editor?
                poly[i].z = 0;
            }

            var j = poly.Length - 1;
            bool inside = false;
            for (int i = 0; i < poly.Length; j = i++)
            {
                if (((poly[i].y <= p.y && p.y < poly[j].y) || (poly[j].y <= p.y && p.y < poly[i].y)) && (p.x < (poly[j].x - poly[i].x) * (p.y - poly[i].y) / (poly[j].y - poly[i].y) + poly[i].x))
                    inside = !inside;
            }

            return inside;
        }

		// TASKS (in CoRoutines)

		#region Tasks and Coroutines

		public static IEnumerator WaitForTask(this System.Threading.Tasks.Task t, CoroutineWaitForTaskResult result=null, float max_time = -1)
        {
            float time = Time.unscaledTime;

            while (!t.IsCompleted && !t.IsFaulted && !t.IsCanceled)
            {
                if (max_time!=-1 && (Time.unscaledTime > (time + max_time)))
                {
                    if (result!=null)
                        result.Exception = new TimeoutException($"WaitForTask timeout max_time={max_time}");

                    yield break;
                }

                yield return null;
            }

            if (result != null)
            {
                result.TaskIsFaulted = t.IsFaulted;
                result.TaskIsCanceled = t.IsCanceled;
            }

            if (!t.IsCompleted)
                Log.Logger.DefaultLog.WriteLine($"WaitForTask() error, IsFaulted={t.IsFaulted}, IsCanceled={t.IsCanceled}");

            yield return null;
        }
        
        public static IEnumerator WaitForTask(Action action, CoroutineWaitForTaskResult result = null, float max_time = -1)
        {
            // WARNING: Never use async actions!!!
            if (action.Method.GetCustomAttribute(typeof(AsyncStateMachineAttribute)) != null)
            {
                if (result != null)
                {
                    result.Exception = new Exception("WaitForTask(): ERROR async Actions are not supported! Use Result");
                    yield break;
                }
            }

            yield return WaitForTask(System.Threading.Tasks.Task.Run(action), result, max_time);
        }

        
		public static IEnumerator ForEachObjectCR<T>(IEnumerable<T> objects, Func<T, bool> action, float delay = 0f)
		{
			foreach (var o in objects)
			{
				if (!action.Invoke(o))
					yield break;

				yield return new WaitForSeconds(delay);
			}
		}

		

		#endregion

		// RECT TRANSFORM


		public static void SetLeft(this RectTransform rt, float left)
        {
            rt.offsetMin = new Vector2(left, rt.offsetMin.y);
        }

        public static void SetRight(this RectTransform rt, float right)
        {
            rt.offsetMax = new Vector2(-right, rt.offsetMax.y);
        }

        public static void SetTop(this RectTransform rt, float top)
        {
            rt.offsetMax = new Vector2(rt.offsetMax.x, -top);
        }

        public static void SetBottom(this RectTransform rt, float bottom)
        {
            rt.offsetMin = new Vector2(rt.offsetMin.x, bottom);
        }

        public static void SetLeftRightTopBottom(this RectTransform rt, float left, float right, float top, float bottom)
		{
            rt.SetLeft(left);
            rt.SetRight(right);
            rt.SetTop(top);
            rt.SetBottom(bottom);
        }
        
        // INPUT

        // check all keys in a range(included)
        public static bool GetKeysDownRange(KeyCode key_from, KeyCode key_to, ref List<KeyCode> keys_down)
        {
            keys_down.Clear();
            for (KeyCode k = key_from; k <= key_to; k++)
            {
                if (Input.GetKeyDown(k))
                    keys_down.Add(k);
            }

            return keys_down.Count > 0;
        }

        // WIN32

#if (UNITY_STANDALONE_WIN || UNITY_EDITOR_WIN)

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern IntPtr GetActiveWindow();

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool ShowWindowAsync(IntPtr hWnd, int nCmdShow);

        [System.Runtime.InteropServices.DllImport("User32.dll")]
        private static extern int SetWindowLong(IntPtr hWnd, int nIndex, long dwNewLong);

        [System.Runtime.InteropServices.DllImport("User32.dll")]
        private static extern int GetWindowLong(IntPtr hWnd, int nIndex);

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool SetWindowPos(IntPtr hWnd, int hWndInsertAfter, int X, int Y, int cx, int cy, uint uFlags);

		[System.Runtime.InteropServices.DllImport("user32.dll")]
		private static extern bool SetForegroundWindow(IntPtr hWnd);

		private const int SW_MAXIMIZE = 3;
        private const int SW_MINIMIZE = 6;
        private const int SW_HIDE = 0;
        private const int SW_SHOW = 5;
		private const int SW_RESTORE = 9;
		private const int SW_SHOWNORMAL = 1;
        private const int SW_SHOWMINIMIZED = 2;
        private const int SW_SHOWMAXIMIZED = 3;
        private const int GWL_EXSTYLE = -0x14;
        private const int WS_EX_TOOLWINDOW = 0x0080;
        private const int GWL_STYLE = -16;
        private const int WS_MAXIMIZEBOX = 0x00010000;
        private const int WS_MINIMIZEBOX = 0x00020000;

        private const long WS_VISIBLE = 0x10000000L;
        private const long WS_EX_APPWINDOW = 0x00040000L;

        private const int SWP_NOSIZE = 0x0001;
        private const int SWP_NOZORDER = 0x0004;


        public static IEnumerator HideWindowCR()
        {
            var hwnd = IntPtr.Zero;

            while (hwnd == IntPtr.Zero)
            {
                hwnd = GetActiveWindow();
                yield return null;
            }

            ShowWindow(hwnd, SW_HIDE);
        }

        public static IEnumerator ExecuteOnWindowHandleAvailable(System.Action action)
        {
            var hwnd = IntPtr.Zero;

            while (hwnd == IntPtr.Zero)
            {
                hwnd = GetActiveWindow();
                yield return null;
            }

            action?.Invoke();
        }

        public static void HideWindow()
        {
            var hwnd = GetActiveWindow();
            ShowWindow(hwnd, SW_HIDE);
        }

        public static void ShowWindow()
        {
            var hwnd = GetActiveWindow();
            ShowWindow(hwnd, SW_SHOW);
        }

        public static void MinimizeWindow()
        {
            var hwnd = GetActiveWindow();
            ShowWindowAsync(hwnd, SW_SHOWMINIMIZED);
        }

        public static void MaximizeWindow()
        {
            var hwnd = GetActiveWindow();
            ShowWindowAsync(hwnd, SW_SHOWMAXIMIZED);
        }

        //NOTE: will turn it into a toolwindow, which defaults to not having taskbar icon, but cannot be minimized
        public static void HideTaskbarIcon()
        {
            var hwnd = GetActiveWindow();
            SetWindowLong(hwnd, GWL_EXSTYLE, GetWindowLong(hwnd, GWL_EXSTYLE) | WS_EX_TOOLWINDOW);
        }
        
        public static void MoveWindow(int X, int Y)
        {
            var hwnd = GetActiveWindow();
            SetWindowPos(hwnd, 0, X, Y, 0, 0, SWP_NOZORDER | SWP_NOSIZE);
        }

        public static void FocusActiveWindow()
        {
			var hwnd = GetActiveWindow();

            if (hwnd == IntPtr.Zero)
            {
				System.Diagnostics.Process currentProcess = System.Diagnostics.Process.GetCurrentProcess();
				hwnd = currentProcess.MainWindowHandle;
			}

			// Restore the window in case it is minimized.
			ShowWindow(hwnd, SW_RESTORE);
			SetForegroundWindow(hwnd);
		}
#endif

#if (UNITY_STANDALONE_OSX || UNITY_EDITOR_OSX)

    [System.Runtime.InteropServices.DllImport("macos_utils", CallingConvention = System.Runtime.InteropServices.CallingConvention.Cdecl)]
    private static extern void SetFocus();
    public static void FocusActiveWindow()
    {
		SetFocus();
	}

#endif



        // EDITOR-RELATED UTILITIES

#if UNITY_EDITOR
        /// <summary>
        /// Used to get assets of a certain type and file extension from entire project
        /// </summary>
        /// <param name="type">The type to retrieve. eg typeof(GameObject).</param>
        /// <param name="fileExtension">The file extention the type uses eg ".prefab".</param>
        /// <returns>An Object array of assets.</returns>
        public static UnityEngine.Object[] GetAssetsOfType(System.Type type, string fileExtension)
        {
            List<UnityEngine.Object> tempObjects = new List<UnityEngine.Object>();
            DirectoryInfo directory = new DirectoryInfo(Application.dataPath);
            FileInfo[] goFileInfo = directory.GetFiles("*" + fileExtension, SearchOption.AllDirectories);

            int i = 0; int goFileInfoLength = goFileInfo.Length;
            FileInfo tempGoFileInfo; string tempFilePath;
            UnityEngine.Object tempGO;
            for (; i < goFileInfoLength; i++)
            {
                tempGoFileInfo = goFileInfo[i];
                if (tempGoFileInfo == null)
                    continue;

                tempFilePath = tempGoFileInfo.FullName;
                tempFilePath = tempFilePath.Replace(@"\", "/").Replace(Application.dataPath, "Assets");

                Debug.Log(tempFilePath + "\n" + Application.dataPath);

                tempGO = AssetDatabase.LoadAssetAtPath(tempFilePath, typeof(UnityEngine.Object)) as UnityEngine.Object;
                if (tempGO == null)
                {
                    Debug.LogWarning("Skipping Null");
                    continue;
                }
                else if (tempGO.GetType() != type)
                {
                    Debug.LogWarning("Skipping " + tempGO.GetType().ToString());
                    continue;
                }

                tempObjects.Add(tempGO);
            }

            return tempObjects.ToArray();
        }
#endif

		#region System Configuration

        public static void DumpSystemConfiguration(string main_product, StringBuilder sb)
        {
			sb.Append($"\nProduct: {main_product}\n");
			sb.Append($"Application: {Application.productName}  version: {Application.version}\n");
			sb.Append($"Copyright: {Application.companyName}\n");

			sb.Append($"ApplicationPath: {Path.GetDirectoryName(Path.GetFullPath(Application.dataPath))}\n");

			sb.Append($"Machine Name:{SystemInfo.deviceName} Model:{SystemInfo.deviceModel}\n");
			sb.Append($"Machine ID: " + SystemInfo.deviceUniqueIdentifier +"\n");
			sb.Append($"GPU: Name:{SystemInfo.graphicsDeviceName} Type:{SystemInfo.graphicsDeviceType} Vendor:{SystemInfo.graphicsDeviceVendor} Version:{SystemInfo.graphicsDeviceVersion} ID:{SystemInfo.graphicsDeviceID}\n");
			sb.Append($"GPU Memory:{SystemInfo.graphicsMemorySize} MaxTextureSize:{SystemInfo.maxTextureSize} ShaderLevel:{SystemInfo.graphicsShaderLevel}\n");

			// NOTE: this will be the GameView window requested size then in Editor
			sb.Append($"Screen: {Screen.width} x {Screen.height}\n");

			// NOTE: this will be always the REAL screen resolution
			sb.Append($"Screen.currentResolution: {Screen.currentResolution.width} x {Screen.currentResolution.height}\n");
            if (Camera.main != null)
			    sb.Append($"Camera: {Camera.main.pixelWidth} x {Camera.main.pixelHeight}");
		}

		#endregion


	}
}
